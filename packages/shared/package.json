{"name": "@memberup/shared", "version": "0.0.0", "type": "module", "description": "memberup shared", "author": "", "homepage": "", "license": "ISC", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./src/*": "./src/*", "./dist/*": "./dist/*"}, "typesVersions": {"*": {"src/*": ["./src/*"], "dist/*": ["./dist/*"]}}, "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@cloudinary/react": "^1.11.2", "@cloudinary/url-gen": "^1.13.0", "@knocklabs/node": "0.4.26", "@mui/styles": "^5.0.0 <6.0.0", "@mux/mux-player": "^3.5.1", "@mux/mux-player-react": "^3.3.0", "@planetscale/database": "^1.19.0", "@prisma/adapter-planetscale": "^5.22.0", "@prisma/client": "5.22.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/full-screen": "^3.12.0", "@react-pdf-viewer/page-navigation": "^3.12.0", "@sentry/nextjs": "^9.14.0", "algoliasearch": "^4.20.0", "axios": "^1.6.4", "bcryptjs": "^2.4.3", "cheerio": "1.1.0", "cloudinary": "^1.41.0", "clsx": "^2.1.1", "color": "^4.2.3", "crypto-js": "4.2.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "file-saver": "^2.0.5", "he": "^1.2.0", "html-react-parser": "^5.0.6", "inngest": "3.29.2", "lodash": "^4.17.21", "luxon": "^3.7.1", "memoize-one": "^6.0.0", "moment-timezone": "^0.5.43", "path": "^0.12.7", "raw-body": "^2.5.2", "react-dropzone": "^14.3.5", "react-player": "2.13.0", "react-toastify": "9.1.3", "sharp": "^0.32.6", "slugify": "^1.6.6", "stream-chat": "^8.40.9", "stripe": "^14.15.0", "superellipsejs": "0.0.6", "unfurl.js": "^6.3.2"}, "peerDependencies": {"@mui/icons-material": "^5.0.0 <6.0.0", "@mui/material": "^5.0.0 <6.0.0"}, "devDependencies": {"@mui/lab": "5.0.0-alpha.173", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/bcryptjs": "^2.4.6", "@types/chai": "^4.3.5", "@types/jest": "^29.5.2", "@types/react": "18.3.11", "@types/react-dom": "18.3.1", "config": "3.3.8", "dotenv": "16.3.1", "eslint": "^9.31.0", "jest": "^29.7.0", "prettier": "^3.0.3", "react": "18.3.1", "start-server-and-test": "^2.0.2", "tsc-alias": "^1.8.8", "tsconfig": "7.0.0", "typescript": "5.8.3"}, "browser": {"child_process": false, "crypto": false}}