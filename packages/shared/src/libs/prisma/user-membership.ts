import { membershipQueryOptions } from 'memberup/lib/query-options/communities'

import prisma from './prisma'
import { USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM } from '@/shared-types/enum'
import { IUserMembership } from '@/shared-types/interfaces'

export async function findUserMembershipAsAdminOrCreator(userId: string, membershipId: string) {
  return await findUserMembership({
    where: {
      user_id: userId,
      membership_id: membershipId,
      status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
      user_role: { in: ['admin', 'owner', 'creator'] },
    },
    include: {
      membership: {
        include: {
          membership_setting: true,
        },
      },
    },
  })
}

export async function findFirstUserMembership(where, include = {}) {
  return prisma.userMembership.findFirst({ where, include })
}

export async function findUserMembership(args: any) {
  const result = await prisma.userMembership.findFirst(args)

  if (result) return result as IUserMembership

  return null
}

export async function findUserMembershipByIds(membershipId, userId, status?) {
  const result = await findUserMembership({
    where: {
      user_id: userId,
      membership_id: membershipId,
      status: status,
    },
    include: {
      user: {
        include: {
          profile: true,
        },
      },
      membership: {
        ...membershipQueryOptions,
      },
    },
  })

  if (result) {
    return result as IUserMembership
  }

  return null
}

export async function findUserMemberships(args: any) {
  const result = await prisma.userMembership.findMany(args)
  return result
}

export async function findAdminsByMembershipId(membershipId: string) {
  return await prisma.userMembership.findMany({
    where: {
      membership_id: membershipId,
      user_role: { not: USER_ROLE_ENUM.member },
      status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
    },
    include: {
      user: true,
    },
  })
}
