import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IUser } from '@/shared-types/interfaces'

export const getUserRole = (user: IUser, membershipId: string) => {
  if (!user || !membershipId) return null

  return user?.user_memberships?.find((um) => um.membership.id === membershipId)?.user_role
}

export const roleCanManageCommunity = (role: USER_ROLE_ENUM) => {
  return [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(role as any)
}

export const userCanManageCommunityById = (user: IUser, membershipId: string) => {
  return roleCanManageCommunity(getUserRole(user, membershipId))
}
