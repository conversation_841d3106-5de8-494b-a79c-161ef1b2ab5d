{"name": "@memberup/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.25.0", "@next/eslint-plugin-next": "^15.3.0", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import-x": "^4.16.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.0", "globals": "^16.0.0", "typescript": "5.8.3", "typescript-eslint": "^8.31.0"}}