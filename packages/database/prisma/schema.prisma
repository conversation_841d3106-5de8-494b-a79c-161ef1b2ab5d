generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model Channel {
  id                                String            @id @default(uuid())
  active                            Boolean?          @default(true)
  allow_member_close_welcome_banner Boolean?          @default(true)
  auto_play_video                   Boolean?          @default(true)
  banner_image                      String?           @db.Text
  banner_image_size                 String?
  banner_image_crop_area            Json?             @default("null") // TAppCropArea?
  description                       String?           @db.Text
  details                           Json? // TChannelDetails[]
  featured_asset                    String?
  featured_asset_type               String?
  name                              String
  sequence                          Int?              @default(0)
  show_giveaways                    Boolean?          @default(true)
  show_space_highlights             Boolean?          @default(true)
  show_upcoming_live_streams        Boolean?          @default(true)
  show_welcome_banner               Boolean?          @default(true)
  slug                              String?
  space_type                        SPACE_TYPE_ENUM   @default(space)
  title                             String?           @db.Text
  top_level                         Boolean?          @default(false)
  type                              CHANNEL_TYPE_ENUM @default(team)
  is_private                        Boolean?          @default(false)
  visibility                        Boolean?          @default(true)
  createdAt                         DateTime?         @default(now()) // DateTime    @default(now())
  updatedAt                         DateTime?         @updatedAt // DateTime @db.Date

  membership    Membership @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id String
  created_by    User       @relation(fields: [created_by_id], references: [id])
  created_by_id String

  notifications Notification[]
  feeds         Feed[]

  @@unique([membership_id, name])
  @@unique([membership_id, slug])
  @@index([membership_id])
  @@index([created_by_id])
  @@map(name: "channels")
}

model Event {
  id                  String                    @id @default(uuid())
  title               String                    @db.Text
  description         String                    @db.Text
  start_time          Int // DateTime with seconds
  end_time            Int // DateTime with seconds
  header_image        String?                   @db.Text
  location_type       EVENT_LOCATION_TYPE_ENUM? @default(in_person)
  location_address    String?
  location_zoom       Int?                      @default(40)
  location_map_center Json? // TGoogleMapCenter?
  location_url        String?
  notify_members      Boolean?
  status              EVENT_STATUS_ENUM?        @default(published)
  time_zone           String?                   @default("America/Los_Angeles")
  createdAt           DateTime?                 @default(now()) // DateTime    @default(now())
  updatedAt           DateTime?                 @updatedAt // DateTime @db.Date

  membership    Membership      @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id String
  creator       User            @relation(fields: [created_by], references: [id], onUpdate: Cascade, onDelete: Cascade)
  created_by    String
  attendees     EventAttendee[]

  @@index([membership_id])
  @@index([created_by])
  @@map(name: "events")
}

model EventAttendee {
  id        String    @id @default(uuid())
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt

  event    Event  @relation(fields: [event_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  event_id String
  user     User   @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id  String

  @@index([event_id])
  @@index([user_id])
  @@map(name: "event_attendees")
}

model Feedback {
  id        String    @id @default(uuid())
  email     String
  feedback  Json
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt

  membership    Membership @relation(fields: [membership_id], references: [id], onDelete: Cascade)
  membership_id String

  @@index([membership_id])
  @@map(name: "feedbacks")
}

model Feed {
  id               String            @id @default(uuid())
  feed_type        FEED_TYPE_ENUM?   @default(default)
  feed_status      FEED_STATUS_ENUM? @default(active)
  text             String?           @db.Text
  html             String?           @db.Text
  attachments      Json? // Attachment[]
  metadata         Json?
  permalink        String?           @unique
  reports          Json? // TReportBy[]
  title            String?           @default(uuid())
  createdAt        DateTime?         @default(now()) // DateTime    @default(now())
  updatedAt        DateTime?         @updatedAt // DateTime @db.Date
  last_activity_at DateTime?
  edited           Boolean?          @default(false)

  user       User    @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id    String
  channel    Channel @relation(fields: [channel_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  channel_id String
  parent_id  String?

  reply_parent_id String?
  replyParent     Feed?   @relation("FeedToFeed", fields: [reply_parent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  replies         Feed[]  @relation("FeedToFeed")

  hierarchy_order String?
  reactions       Reaction[]
  notifications   Notification[]
  feed_tracks     FeedTrack[]

  @@index([permalink(length: 191)])
  @@index([channel_id])
  @@index([user_id])
  @@index([createdAt])
  @@index([reply_parent_id])
  @@index([parent_id, hierarchy_order])
  @@map(name: "feeds")
}

model FeedTrack {
  id        String    @id @default(uuid())
  createdAt DateTime? @default(now()) // DateTime    @default(now())
  updatedAt DateTime? @updatedAt // DateTime @db.Date
  user      User      @relation(fields: [user_id], references: [id])
  user_id   String
  feed      Feed      @relation(fields: [feed_id], references: [id], onDelete: Cascade)
  feed_id   String

  @@unique([user_id, feed_id])
  @@index([user_id])
  @@index([feed_id])
  @@index([user_id, feed_id])
  @@map(name: "feed_tracks")
}

model ContentLibrary {
  id          String    @id @default(uuid())
  title       String
  description String?   @db.Text
  thumbnail   Json?
  metadata    Json?
  createdAt   DateTime? @default(now()) // DateTime    @default(now())
  updatedAt   DateTime? @updatedAt // DateTime @db.Date

  membership                       Membership                         @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id                    String
  ContentLibraryCourse             ContentLibraryCourse[]
  ContentLibraryCourseLesson       ContentLibraryCourseLesson[]
  ContentLibraryCourseSection      ContentLibraryCourseSection[]
  ContentLibraryCourseUserProgress ContentLibraryCourseUserProgress[]

  @@unique([membership_id])
  @@index([membership_id])
  @@map(name: "content_libraries")
}

model ContentLibraryCourse {
  id                   String                         @id @default(uuid())
  title                String
  description          String?                        @db.Text
  only_for_annual_plan Boolean?                       @default(false)
  visibility           LIBRARY_COURSE_VISIBILITY_ENUM
  thumbnail            Json?
  metadata             Json?
  createdAt            DateTime?                      @default(now()) // DateTime    @default(now())
  updatedAt            DateTime?                      @updatedAt // DateTime @db.Date

  membership                       Membership                         @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id                    String
  contentLibrary                   ContentLibrary?                    @relation(fields: [content_library_id], references: [id], onDelete: Cascade)
  content_library_id               String
  ContentLibraryCourseLesson       ContentLibraryCourseLesson[]
  ContentLibraryCourseSection      ContentLibraryCourseSection[]
  ContentLibraryCourseUserProgress ContentLibraryCourseUserProgress[]

  @@index([membership_id])
  @@index([content_library_id])
  @@map(name: "content_library_courses")
}

model ContentLibraryCourseUserProgress {
  id                        String               @id @default(uuid())
  progress_percentage       Float                @default(0.00)
  done                      Json                 @default("{}")
  createdAt                 DateTime?            @default(now()) // DateTime    @default(now())
  updatedAt                 DateTime?            @updatedAt // DateTime @db.Date
  user                      User                 @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id                   String
  membership                Membership           @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id             String
  ContentLibraryCourse      ContentLibraryCourse @relation(fields: [content_library_course_id], references: [id])
  content_library_course_id String
  ContentLibrary            ContentLibrary?      @relation(fields: [content_library_id], references: [id], onDelete: Cascade)
  content_library_id        String?

  @@unique([user_id, membership_id, content_library_course_id, content_library_id])
  @@unique([content_library_course_id, user_id])
  @@index([membership_id])
  @@index([user_id])
  @@index([content_library_course_id])
  @@index([content_library_id])
  @@map(name: "content_library_course_progress")
}

model ContentLibraryCourseSection {
  id        String    @id @default(uuid())
  name      String
  createdAt DateTime? @default(now()) // DateTime    @default(now())
  updatedAt DateTime? @updatedAt // DateTime @db.Date

  membership                 Membership                             @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id              String
  ContentLibraryCourseLesson ContentLibraryCourseLesson[]
  ContentLibraryCourse       ContentLibraryCourse?                  @relation(fields: [content_library_course_id], references: [id])
  content_library_course_id  String?
  visibility                 LIBRARY_COURSE_SECTION_VISIBILITY_ENUM @default(published)
  sequence                   Int?                                   @default(1)
  ContentLibrary             ContentLibrary?                        @relation(fields: [content_library_id], references: [id], onDelete: Cascade)
  content_library_id         String?

  @@index([content_library_course_id])
  @@index([membership_id])
  @@index([content_library_id])
  @@map(name: "content_library_course_sections")
}

model ContentLibraryCourseLesson {
  id                  String                  @id @default(uuid())
  visibility          LIBRARY_VISIBILITY_ENUM
  title               String
  type                LESSON_TYPE_ENUM?
  text                String?                 @db.Text
  thumbnail_url       String?
  media_file_title    String?
  media_file_subtitle String?
  media_file          Json?
  resource_files      Json?
  createdAt           DateTime?               @default(now()) // DateTime    @default(now())
  updatedAt           DateTime?               @updatedAt // DateTime @db.Date
  membership          Membership              @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id       String
  media_transcript    String?                 @db.LongText

  section            ContentLibraryCourseSection @relation(fields: [section_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  section_id         String
  ContentLibrary     ContentLibrary?             @relation(fields: [content_library_id], references: [id], onDelete: Cascade)
  content_library_id String?

  ContentLibraryCourse      ContentLibraryCourse? @relation(fields: [content_library_course_id], references: [id])
  content_library_course_id String?
  sequence                  Int?                  @default(1)

  @@index([membership_id])
  @@index([section_id])
  @@index([content_library_course_id])
  @@index([content_library_id])
  @@map(name: "content_library_course_lessons")
}

model InviteLink {
  id            String          @id @default(uuid())
  email         String?
  role          USER_ROLE_ENUM?
  url           String          @db.Text
  token         String          @db.Text
  active        Boolean?        @default(true)
  createdAt     DateTime?       @default(now()) // DateTime    @default(now())
  updatedAt     DateTime?       @updatedAt // DateTime @db.Date
  membership_id String

  @@map(name: "invite_links")
}

model Live {
  id                      String                     @id @default(uuid())
  title                   String?                    @db.Text
  description             String?                    @db.Text
  broadcast               LIVE_STREAM_BRODCAST_ENUM? @default(now)
  allow_comment           Boolean?                   @default(true)
  announcement_post_image String?
  scheduled_timestamp     Int?
  status                  MESSAGE_STATUS_ENUM?       @default(approved)
  stream_id               String?
  playback                String?
  thumbnail               String?
  createdAt               DateTime?                  @default(now()) // DateTime    @default(now())
  updatedAt               DateTime?                  @updatedAt // DateTime @db.Date

  membership    Membership @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id String
  user          User       @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id       String

  @@index([user_id])
  @@index([membership_id])
  @@map(name: "lives")
}

// TODO: delete all fields related to Stripe, except those that are for the community as a product
model MembershipSetting {
  id                    String              @id @default(uuid())
  active_campaign       Json? // TActiveCampaign?
  assets_path           String?
  completed_membership  Int?                @default(0)
  community_idea        Boolean?            @default(false)
  custom_host           String?
  custom_host_redirect  String?
  custom_host_disabled  Boolean?
  custom_host_verified  Boolean?
  custom_host_cert      Json? // https://vercel.com/docs/rest-api/endpoints#issue-a-new-cert
  emails                Json? // TEmailNotice[]
  enable_intro_page     Boolean?
  favicon               String?
  favicon_crop_area     Json?               @default("null") // AppCropArea?
  description           String?
  intro_html            String?             @db.Text
  intro_closed_html     String?             @db.Text
  onboarding            Json? // TOnboarding?
  plan                  MEMBERUP_PLAN_ENUM?
  is_paid               Boolean             @default(false)
  logo                  String?
  cover_image           String?
  cover_image_crop_area Json?
  logo_crop_area        Json?               @default("null") // AppCropArea?
  support_email         String?
  library               Json? // TMembershipSettingLibrary?
  referral_message      String?             @default("Want a site like mine? Check out <u>Memberup</u>")
  show_referral_message Boolean?            @default(true)
  about_gallery         Json?
  about_text            String?             @db.Text
  about_title           String?             @db.Text
  members_count         Int?                @default(0)
  signin                Json? // TMembershipSettingSignin?
  signup                Json? // TMembershipSettingSignup?
  signup_payment        Json? // TMembershipSettingSignupPayment?

  active_spark_category                         SparkMembershipCategory?         @relation(fields: [active_spark_category_id], references: [id], onUpdate: Cascade)
  active_spark_category_id                      String?
  spark_enabled                                 Boolean?                         @default(false)
  spark_current_membership_category_id          String?
  spark_expire_time                             Json? // TAppTime?
  spark_current_membership_question_id          String?
  spark_current_membership_question_instance_id String?
  is_pricing_enabled                            Boolean?
  stripe_enable_annual                          Boolean?                         @default(true)
  stripe_live_mode                              Boolean?                         @default(true)
  stripe_customer_id                            String? // for memberup stripe
  stripe_payment_method_id                      String? // for memberup stripe
  stripe_prices                                 Json? // for membership stripe
  stripe_product_id                             String? // for memberup stripe
  stripe_subscription_id                        String? // for memberup stripe
  stripe_subscription_canceled_at               Int? // for memberup stripe
  stripe_subscription_intent_client_secret      String?
  stripe_subscription_invoice_id                String?
  stripe_subscription_discount_id               String?
  stripe_subscription_status                    STRIPE_SUBSCRIPTION_STATUS_ENUM?
  stripe_subscription_period_start_at           Int?
  stripe_subscription_period_end_at             Int?
  billing_cycle                                 BILLING_CYCLE_ENUM?
  stripe_connect_account                        Json? // TStripeConnectAccount? // for membership stripe
  stripe_metadata_mode                          String? // lifetime
  theme_main_color                              String?                          @default("#DB8BE7")
  theme_secondary_color                         String?                          @default("linear-gradient(90deg, #C487D5 0%, #E18FB9 51%, #E99B94 100%)")
  theme_mode                                    THEME_MODE_ENUM                  @default(dark)
  time_zone                                     String?                          @default("America/Los_Angeles")
  visibility                                    VISIBILITY_ENUM                  @default(private)
  discoverable                                  Boolean?                         @default(true)
  external_links                                Json?
  form_enabled                                  Boolean                          @default(false)
  form                                          Json?
  createdAt                                     DateTime?                        @default(now())
  updatedAt                                     DateTime?                        @updatedAt

  membership    Membership @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id String     @unique

  @@index([active_spark_category_id])
  @@map(name: "membership_settings")
}

model Membership {
  id              String    @id @default(uuid())
  active          Boolean?  @default(false)
  brand           String
  domain          String?
  deactive_reason String?
  name            String
  slug            String    @unique
  status          COMMUNITY_STATUS_ENUM? @default(pending_authorization)
  createdAt       DateTime? @default(now()) // DateTime    @default(now())
  updatedAt       DateTime? @updatedAt // DateTime @db.Date

  owner_id              String?
  owner                 User?       @relation("MembershipOwner", fields: [owner_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
  referrer_user         User?       @relation("ReferrerUser", fields: [referrer_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  // User who will be credited affiliate commissions for this community
  referrer_user_id      String?
  // Community that referred this community. Only used for analytics.
  referrer_community    Membership? @relation("ReferrerCommunity", fields: [referrer_community_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  referrer_community_id String?

  channels                         Channel[]
  feedbacks                        Feedback[]
  lives                            Live[]
  libraries                        Library[]
  library_categories               LibraryCategory[]
  membership_setting               MembershipSetting?
  tags                             Tag[]
  users                            User[]
  events                           Event[]
  ContentLibrary                   ContentLibrary[]
  ContentLibraryCourseSection      ContentLibraryCourseSection[]
  ContentLibraryCourse             ContentLibraryCourse[]
  ContentLibraryCourseLesson       ContentLibraryCourseLesson[]
  user_memberships                 UserMembership[]
  ContentLibraryCourseUserProgress ContentLibraryCourseUserProgress[]
  // Communities that were referred through links in this community
  referring_communities            Membership[]                       @relation("ReferrerCommunity")
  // Payment history for this membership
  payments                         Payment[]
  // Subscription for this membership
  subscriptions                    Subscription[]
  // Audit logs for this membership
  audit_logs                       AuditLog[]

  @@index([owner_id])
  @@index([referrer_community_id])
  @@index([referrer_user_id])
  @@index([name])
  @@map(name: "memberships")
}

model Notification {
  id                 String    @id @default(uuid())
  // channel_id String
  cid                String?
  channel_type       String
  created_at         String
  received_at        String
  total_unread_count Int
  type               String
  watcher_count      Int?
  createdAt          DateTime? @default(now()) // DateTime    @default(now())
  updatedAt          DateTime? @updatedAt // DateTime @db.Date

  channel     Channel  @relation(fields: [channel_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  channel_id  String
  message     Feed     @relation(fields: [message_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  message_id  String
  reaction    Reaction @relation(fields: [reaction_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  reaction_id String
  user        User     @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id     String

  @@index([channel_id])
  @@index([reaction_id])
  @@index([message_id])
  @@index([user_id])
  @@map(name: "notifications")
}

model Reaction {
  id             String    @id @default(uuid())
  enforce_unique Boolean?
  metadata       Json?
  type           String
  text           String?   @db.Text
  createdAt      DateTime? @default(now()) // DateTime    @default(now())
  updatedAt      DateTime? @updatedAt // DateTime @db.Date

  message       Feed           @relation(fields: [message_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  message_id    String
  user          User           @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id       String
  notifications Notification[]

  @@unique([type, message_id, user_id], name: "type_message_user_unique")
  @@index([user_id])
  @@index([message_id])
  @@map(name: "reactions")
}

model SparkCategory {
  id          String    @id @default(uuid())
  name        String
  slug        String
  icon        String
  description String    @db.Text
  active      Boolean?  @default(true)
  seed_id     String?
  createdAt   DateTime? @default(now()) // DateTime    @default(now())
  updatedAt   DateTime? @updatedAt // DateTime @db.Date

  membership_id   String?
  spark_questions SparkQuestion[]

  @@map(name: "spark_categories")
}

model SparkQuestion {
  id        String    @id @default(uuid())
  content   String    @db.Text
  answer    String?   @db.Text
  active    Boolean?  @default(true)
  seed_id   String?
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt

  category      SparkCategory @relation(fields: [category_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  category_id   String
  membership_id String?

  @@index([category_id])
  @@map(name: "spark_questions")
}

model SparkSeedCategory {
  id                   String              @id @default(uuid())
  name                 String
  enabled              Boolean?            @default(true)
  createdAt            DateTime?           @default(now()) // DateTime    @default(now())
  updatedAt            DateTime?           @updatedAt // DateTime @db.Date
  spark_seed_questions SparkSeedQuestion[]
  @@map(name: "spark_seed_categories")
}

model SparkSeedQuestion {
  id        String    @id @default(uuid())
  content   String    @db.Text
  enabled   Boolean?  @default(true)
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt
  spark_seed_category    SparkSeedCategory @relation(fields: [spark_seed_category_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  spark_seed_category_id String
  @@index([spark_seed_category_id])
  @@map(name: "spark_seed_questions")
}

model SparkMembershipCategory {
  id                         String                    @id @default(uuid())
  name                       String
  enabled                    Boolean?                  @default(true)
  createdAt                  DateTime?                 @default(now()) // DateTime    @default(now())
  updatedAt                  DateTime?                 @updatedAt // DateTime @db.Date
  spark_seed_category_id     String?
  question_sequences         Json?
  membership_id              String?
  membership_settings        MembershipSetting[]
  spark_membership_questions SparkMembershipQuestion[]

  @@index([membership_id])
  @@map(name: "spark_membership_categories")
}

model SparkMembershipQuestion {
  id                           String                  @id @default(uuid())
  content                      String                  @db.Text
  enabled                      Boolean?                @default(true)
  createdAt                    DateTime?               @default(now())
  updatedAt                    DateTime?               @updatedAt
  spark_membership_category    SparkMembershipCategory @relation(fields: [spark_membership_category_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  spark_membership_category_id String
  membership_id                String?

  @@index([spark_membership_category_id])
  @@map(name: "spark_membership_questions")
}

model SparkMembershipQuestionInstance {
  id                                           String                                    @id @default(uuid())
  content                                      String                                    @db.Text
  spark_membership_question_id                 String?
  state                                        SPARK_MEMBERSHIP_QUESTION_INSTANCE_STATE
  createdAt                                    DateTime?                                 @default(now()) // DateTime    @default(now())
  updatedAt                                    DateTime?                                 @updatedAt // DateTime @db.Date
  spark_membership_question_instance_responses SparkMembershipQuestionInstanceResponse[]
  membership_id                                String?

  @@map(name: "spark_membership_question_instances")
}

model SparkMembershipQuestionInstanceResponse {
  id        String    @id @default(uuid())
  content   String    @db.Text
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt

  spark_membership_question_instance    SparkMembershipQuestionInstance @relation(fields: [spark_membership_question_instance_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  spark_membership_question_instance_id String

  membership_id String
  user          User   @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id       String
  streak_count  Int    @default(0)

  @@unique([user_id, spark_membership_question_instance_id])
  @@index([spark_membership_question_instance_id])
  @@index([user_id])
  @@map(name: "spark_membership_question_instance_responses")
}

model Tag {
  id        String    @id @default(uuid())
  name      String
  active    Boolean?  @default(false)
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt

  membership    Membership @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id String

  @@index([membership_id])
  @@map(name: "tags")
}

model UserProfile {
  id                                       String                           @id @default(uuid())
  active                                   Boolean?                         @default(false)
  about                                    String?                          @db.Text
  bio                                      String?                          @db.Text
  completed_image                          Boolean?
  completed_profile                        Boolean?                         @default(false)
  create_post_reminder                     Boolean?                         @default(false)
  enable_notifications                     Json? // for membership stripe
  getting_started                          Json? // TaskStatus?
  image                                    String?                          @db.Text
  image_crop_area                          Json?                            @default("null") //
  cover_image                              String?                          @db.Text
  cover_image_crop_area                    Json?                            @default("null") // TAppCropArea?
  hide_my_activity                         Boolean?                         @default(false)
  library_layout                           LIBRARY_LAYOUT_ENUM?             @default(grid)
  phone_number                             String?
  relationships                            Json?
  affiliate                                Json? // TAffiliateSchema
  referral_message                         String?                          @default("Want a site like mine? Check out <u>Memberup</u>")
  show_referral_message                    Boolean?                         @default(true)
  stripe_customer_id                       String?
  stripe_payment_method_id                 String?
  stripe_subscription_canceled_at          Int?
  stripe_subscription_id                   String?
  stripe_subscription_intent_client_secret String?
  stripe_subscription_status               STRIPE_SUBSCRIPTION_STATUS_ENUM?
  stripe_subscription_period_start_at      Int?
  stripe_subscription_period_end_at        Int?
  stripe_subscription_cancel_at_period_end Boolean?
  spark_hidden_at                          DateTime?
  pinned_posts_hidden                      Json?
  stripe_metadata_mode                     String? // lifetime
  time_zone                                String?
  location                                 String?                          @db.Text
  social                                   Json?
  personality_type                         PERSONALITY_TYPE_ENUM?
  createdAt                                DateTime?                        @default(now()) // DateTime    @default(now())
  updatedAt                                DateTime?                        @updatedAt // DateTime @db.Date
  last_login_at                            DateTime?
  last_session_initialized_at              DateTime?
  last_activity_at                         DateTime?
  login_count                              Int?                             @default(0)
  theme_mode                               THEME_MODE_ENUM?

  user    User   @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id String @unique

  @@map(name: "user_profiles")
}

model User {
  id                  String            @id @default(uuid())
  email               String
  password            String?
  first_name          String
  last_name           String
  username            String? // TODO: Remove optional after migrating all users
  image               String?           @db.Text
  invite_token        String?           @db.Text
  reset_token         String?           @db.Text
  related_users       Json?
  is_primary          Boolean           @default(false)
  role                USER_ROLE_ENUM?   @default(member)
  status              USER_STATUS_ENUM? @default(unverified)
  verification_code   String?
  banned_reason       String?
  createdAt           DateTime?         @default(now()) // DateTime    @default(now())
  updatedAt           DateTime?         @updatedAt // DateTime @db.Date
  name_updated_at     DateTime?
  username_updated_at DateTime?

  memberships_owned                Membership[]                              @relation("MembershipOwner")
  user_memberships                 UserMembership[]
  membership                       Membership?                               @relation(fields: [membership_id], references: [id], onUpdate: NoAction, onDelete: NoAction)
  membership_id                    String?
  channels                         Channel[]
  event_attendees                  EventAttendee[]
  events                           Event[]
  feeds                            Feed[]
  lives                            Live[]
  notifications                    Notification[]
  reactions                        Reaction[]
  spark_responses                  SparkMembershipQuestionInstanceResponse[]
  profile                          UserProfile?
  feed_tracks                      FeedTrack[]
  action_history_records           ActionHistory[]
  actions_per_day                  ActionsPerDay[]
  ContentLibraryCourseUserProgress ContentLibraryCourseUserProgress[]
  libraries                        Library[]

  // Communities for which this user is a referrer
  referring_communities            Membership[]           @relation("ReferrerUser")
  // Payment history for this user
  payments                         Payment[] // TODO: delete
  // Audit logs triggered by this user
  audit_logs                       AuditLog[]

  @@unique([username])
  @@index([membership_id], name: "membership_id_idx")
  @@map(name: "users")
}

// TODO: delete all the stripe fields
model UserMembership {
  id        String    @id @default(uuid())
  createdAt DateTime? @default(now()) // DateTime    @default(now())
  updatedAt DateTime? @updatedAt // DateTime @db.Date

  status USER_MEMBERSHIP_STATUS_ENUM? @default(pending)

  membership    Membership      @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id String
  user          User            @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade) // TODO: delete
  user_id       String          // TODO: delete
  user_role     USER_ROLE_ENUM? @default(member)

  stripe_customer_id                       String?
  stripe_payment_method_id                 String?
  stripe_subscription_canceled_at          Int?
  stripe_subscription_id                   String?
  stripe_subscription_intent_client_secret String?
  stripe_subscription_status               STRIPE_SUBSCRIPTION_STATUS_ENUM?
  stripe_subscription_period_start_at      Int?
  stripe_subscription_period_end_at        Int?
  stripe_subscription_cancel_at_period_end Boolean?
  user_answer                              Json?
  rejection_date                           DateTime?
  rejected_by                              String?

  subscriptions Subscription[]
  audit_logs    AuditLog[]
  payments      Payment[]

  @@unique([user_id, membership_id])
  @@index([user_id], name: "user_id_idx")
  @@index([membership_id], name: "membership_id_idx")
  @@map(name: "user_memberships")
}

model Action {
  id                     String           @id @default(uuid())
  action_name            ACTION_NAME_ENUM @default(LIKE)
  points                 Int
  action_history_records ActionHistory[]

  @@unique([action_name])
  @@map(name: "actions")
}

model ActionHistory {
  id          String   @id @default(uuid())
  user_id     String
  user        User     @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  action_id   String
  action      Action   @relation(fields: [action_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  resource_id String?
  createdAt   DateTime @default(now())

  @@unique([user_id, action_id, resource_id])
  @@index([action_id])
  @@index([user_id])
  @@map(name: "action_history")
}

model ActionsPerDay {
  id      String   @id @default(uuid())
  user_id String
  user    User     @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  day     DateTime @db.Date
  count   Int      @default(0)

  @@unique([user_id, day])
  @@index([user_id])
  @@index([day])
  @@map(name: "actions_per_day")
}

model Library {
  id              String                @id @default(uuid())
  allow_comment   Boolean?              @default(true)
  mimetype        LIBRARY_CONTENT_ENUM? @default(other)
  description     String?               @db.Text
  filename        String?
  featured_at     Int?
  mux_asset       Json? // TMuxAsset?
  mux_upload_id   String?
  mux_upload_url  String?
  notify_members  Boolean?
  passthrough     String?
  published_at    DateTime?
  status          LIBRARY_STATUS_ENUM?  @default(drafts)
  sequence        Int?                  @default(0)
  thumbnails      String?
  title           String?               @db.Text
  url             String?               @db.Text
  allow_access_to Json?                 @default("[]")
  assets          Json?                 @default("[]")
  categories      Json?                 @default("[]")
  tags            Json?                 @default("[]")
  createdAt       DateTime?             @default(now()) // DateTime    @default(now())
  updatedAt       DateTime?             @updatedAt // DateTime @db.Date

  membership        Membership        @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id     String
  user              User              @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  user_id           String
  library_sequences LibrarySequence[]

  @@index([membership_id])
  @@index([user_id])
  @@map(name: "libraries")
}

model LibrarySequence {
  id        String    @id @default(uuid())
  sequence  Int?      @default(0)
  createdAt DateTime? @default(now()) // DateTime    @default(now())
  updatedAt DateTime? @updatedAt // DateTime @db.Date

  library             Library         @relation(fields: [library_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  library_id          String
  library_category    LibraryCategory @relation(fields: [library_category_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  library_category_id String

  @@index([library_id])
  @@index([library_category_id])
  @@map(name: "library_sequences")
}

model LibraryCategory {
  id          String    @id @default(uuid())
  name        String?
  description String?   @db.Text
  default     Boolean?  @default(false)
  sequence    Int?      @default(0)
  createdAt   DateTime? @default(now()) // DateTime    @default(now())
  updatedAt   DateTime? @updatedAt // DateTime @db.Date

  membership        Membership        @relation(fields: [membership_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  membership_id     String
  library_sequences LibrarySequence[]

  @@index([membership_id])
  @@map(name: "library_categories")
}

model Payment {
  id                       String              @id @default(uuid())
  amount                   Decimal             @db.Decimal(10, 2)
  currency                 String              @default("usd")      // TODO: delete
  description              String?                                  // TODO: delete
  payment_date             DateTime?
  status                   PAYMENT_STATUS_ENUM
  payment_type             PAYMENT_TYPE_ENUM
  stripe_invoice_id        String?
  stripe_payment_intent_id String?
  stripe_charge_id         String?
  stripe_receipt_url       String?
  stripe_customer_id       String?
  stripe_invoice           Json?               // TODO: delete
  created_at               DateTime            @default(now())
  updated_at               DateTime            @updatedAt
  
  user                     User?               @relation(fields: [user_id], references: [id], onUpdate: Cascade, onDelete: Cascade)       // TODO: delete
  user_id                  String?             // TODO: delete
  membership               Membership?         @relation(fields: [membership_id], references: [id], onUpdate: Restrict, onDelete: Restrict)
  membership_id            String?
  user_membership          UserMembership?     @relation(fields: [user_membership_id], references: [id], onUpdate: Restrict, onDelete: Restrict)
  user_membership_id       String?             @unique

  @@index([user_id])
  @@index([membership_id])
  @@index([user_membership_id])
  @@index([stripe_invoice_id])
  @@index([stripe_customer_id])
  @@map(name: "payments")
}

model Subscription {
  id                        String                           @id @default(uuid())
  customer_id               String?
  payment_method_id         String?
  subscription_id           String?
  canceled_at               DateTime?
  intent_client_secret      String?
  invoice_id                String?
  discount_id               String?
  status                    STRIPE_SUBSCRIPTION_STATUS_ENUM?
  period_start_at           DateTime?
  period_end_at             DateTime?
  cancel_at_period_end      Boolean?
  connect_account           Json?
  billing_cycle             BILLING_CYCLE_ENUM?
  created_at                DateTime                         @default(now())
  updated_at                DateTime                         @updatedAt

  // Relations
  membership        Membership?     @relation(fields: [membership_id], references: [id], onUpdate: Restrict, onDelete: Restrict)
  membership_id     String?         @unique
  user_membership   UserMembership? @relation(fields: [user_membership_id], references: [id], onUpdate: Restrict, onDelete: Restrict)
  user_membership_id String?        @unique

  @@index([membership_id])
  @@index([user_membership_id])
  @@index([customer_id])
  @@index([subscription_id])
  @@map(name: "subscriptions")
}

model AuditLog {
  id                 String              @id @default(uuid())
  source             AUDIT_LOG_SOURCE_ENUM
  data               Json
  created_at         DateTime            @default(now())

  // Relations
  membership         Membership?         @relation(fields: [membership_id], references: [id], onUpdate: Restrict, onDelete: SetNull)
  membership_id      String?
  user_membership    UserMembership?     @relation(fields: [user_membership_id], references: [id], onUpdate: Restrict, onDelete: SetNull)
  user_membership_id String?
  user               User?               @relation(fields: [user_id], references: [id], onUpdate: Restrict, onDelete: SetNull)
  user_id            String?

  @@index([membership_id])
  @@index([user_membership_id])
  @@index([user_id])
  @@map(name: "audit_logs")
}

enum BILLING_CYCLE_ENUM {
  monthly
  annual
  one_time
}

enum SPARK_MEMBERSHIP_QUESTION_INSTANCE_STATE {
  undefined
  started
  completed
}

enum ACTION_NAME_ENUM {
  LIKE_UNLIKE
  LIKE
  USER_ACTIVE
  LESSON_COMPLETED
  POST_CREATED
  COMMENT_CREATED
}

enum ACTIVITY_ENUM {
  library
  live
  post
}

enum BANNER_IMAGE_SIZE_ENUM {
  short
  tall
  long
}

enum CHANNEL_TYPE_ENUM {
  livestream
  messaging
  team
  gaming
  commerce
  library
  live
  spark
}

enum FEED_TYPE_ENUM {
  default
  post
  comment
}

enum FEED_STATUS_ENUM {
  active
  approved
  reported
  rejected
}

enum LAYOUT_ENUM {
  auth_layout
  page_layout
  live_layout
}

enum USER_ROLE_ENUM {
  admin
  creator
  member
  owner
}

enum USER_MEMBERSHIP_STATUS_ENUM {
  pending
  canceled
  active
  rejected
  banned
  payment_pending // TODO: delete
  accepted // TODO: delete
  cancelled // TODO: delete
}

enum COMMUNITY_STATUS_ENUM {
  pending_authorization
  trialing
  active
  canceled
}

enum USER_STATUS_ENUM {
  unverified
  active
  accepted
  banned
  deleted
  inactive
  invited
}

enum EVENT_LOCATION_TYPE_ENUM {
  in_person
  url
  content_release
  tbd
}

enum EVENT_STATUS_ENUM {
  drafts
  published
}

enum LIVE_STREAM_BRODCAST_ENUM {
  now
  schedule
}

enum MESSAGE_STATUS_ENUM {
  active
  approved
  reported
  rejected
  received
  sending
  failed
}

enum SPACE_TYPE_ENUM {
  home
  space
}

enum LIBRARY_LAYOUT_ENUM {
  grid
  list
  row
}

enum PERSONALITY_TYPE_ENUM {
  DS
  ISTJ
  ISTP
  ISFJ
  ISFP
  INFJ
  INFP
  INTJ
  INTP
  ESTP
  ESTJ
  ESFP
  ESFJ
  ENFP
  ENFJ
  ENTP
  ENTJ
}

enum LIBRARY_STATUS_ENUM {
  drafts
  published
}

enum LIBRARY_CONTENT_ENUM {
  audio
  doc
  epub
  image
  pdf
  ppt
  video
  other
}

enum MEMBERUP_PLAN_ENUM {
  basic
  pro
  enterprise
  enterprise_monthly
  enterprise_annual
}

enum STRIPE_SUBSCRIPTION_STATUS_ENUM {
  incomplete
  incomplete_expired
  trialing
  active
  past_due
  canceled
  unpaid
}

enum THEME_MODE_ENUM {
  dark
  light
}

enum VISIBILITY_ENUM {
  open
  closed
  public
  private
}

enum LIBRARY_VISIBILITY_ENUM {
  draft
  published
}

enum LIBRARY_COURSE_VISIBILITY_ENUM {
  draft
  published
  deleted
}

enum LIBRARY_COURSE_SECTION_VISIBILITY_ENUM {
  draft
  published
}

enum LESSON_TYPE_ENUM {
  text
  video
  audio
}

enum PAYMENT_TYPE_ENUM {
  membership
  memberup
}

enum PAYMENT_STATUS_ENUM {
  paid
  refunded
  failed
  pending
}

enum SUBSCRIPTION_TYPE_ENUM {
  community
  membership
}

enum AUDIT_LOG_SOURCE_ENUM {
  stripe
}
