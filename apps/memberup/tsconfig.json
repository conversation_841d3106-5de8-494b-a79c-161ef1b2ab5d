{"extends": "../../packages/tsconfig/nextjs.json", "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.jsx", ".next/types/**/*.ts"], "exclude": ["node_modules"], "compilerOptions": {"baseUrl": ".", "paths": {"@/memberup/components/*": ["./src/components/*"], "@/memberup/layouts/*": ["./src/layouts/*"], "@/memberup/libs/*": ["./src/libs/*"], "@/memberup/pages/*": ["./pages/*"], "@/memberup/middlewares/*": ["./src/middlewares/*"], "@/memberup/settings/*": ["./src/settings/*"], "@/memberup/store/*": ["./src/store/*"], "@/memberup/test/*": ["./__test__/*"], "@/shared-components/*": ["../../packages/shared/src/components/*"], "@/shared-config/*": ["../../packages/shared/src/config/*"], "@/shared-libs/*": ["../../packages/shared/src/libs/*"], "@/shared-models/*": ["../../packages/shared/src/models/*"], "@/shared-services/*": ["../../packages/shared/src/services/*"], "@/shared-settings/*": ["../../packages/shared/src/settings/*"], "@/shared-styles/*": ["../../packages/shared/src/styles/*"], "@/shared-types/*": ["../../packages/shared/src/types/*"], "@/*": ["./*"], "memberup/*": ["./*"]}, "plugins": [{"name": "next"}], "strictNullChecks": false, "types": ["node", "jest"]}}