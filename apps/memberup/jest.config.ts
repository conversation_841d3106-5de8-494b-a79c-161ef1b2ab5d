import nextJest from 'next/jest'
import { pathsToModuleNameMapper } from 'ts-jest'

import { compilerOptions } from './tsconfig.json'

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  testEnvironment: 'jest-environment-node',
  preset: 'ts-jest/presets/default-esm',
  globals: {
    'ts-jest': {
      useESM: true,
    },
  },
  // TODO: this is to resolve an issue traying to load @auth/core but is not working properly, we need to find a better solution
  transformIgnorePatterns: ['node_modules/(?!(@auth/core|@auth/.*|next-auth|jose|@panva/hkdf|oauth4webapi)/)'],
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, { prefix: '<rootDir>/' }),
}

export default createJestConfig(customJestConfig)
