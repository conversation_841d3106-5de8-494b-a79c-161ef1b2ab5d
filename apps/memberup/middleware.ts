import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

import { nonCommunityPathnames } from './lib/constants'

export function middleware(request: NextRequest) {
  const pathname = new URL(request.url).pathname
  const response = NextResponse.next()
  response.headers.set('x-pathname', pathname)

  // <PERSON>le redirects from old post URLs to new format
  // Old: /[community]/post/[postId] -> New: /[community]/[postId]
  const postRedirectMatch = pathname.match(/^\/([^/]+)\/post\/(.+)$/)
  if (postRedirectMatch) {
    const [, communitySlug, postId] = postRedirectMatch
    const newUrl = new URL(request.url)
    newUrl.pathname = `/${communitySlug}/${postId}`

    const commentId = request.nextUrl.searchParams.get('comment_id')
    if (commentId) {
      newUrl.searchParams.set('comment_id', commentId)
    }

    return NextResponse.redirect(newUrl, 301)
  }

  if (pathname && !pathname.startsWith('/@')) {
    const pathnameParts = pathname.split('/')

    if (pathnameParts.length > 1 && !nonCommunityPathnames.includes(`/${pathnameParts[1]}`)) {
      response.headers.set('x-community-slug', pathnameParts[1])
    }
  }

  return response
}

export const config = {
  matcher: '/((?!api/|_next/|assets/|favicon.ico|robots.txt|sitemap.xml).*)',
}
