'use client'

import MiniSearch from 'minisearch'
import Link from 'next/link'
import { useMemo, useState } from 'react'

import { Favicon } from '@/components/community/favicon'
import { Plus16Icon } from '@/components/icons'
import { ScrollArea } from '@/components/ui'
import { StandardInput } from '@/components/ui/standard-input'
import { useStore } from '@/hooks/useStore'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'
import { IMembership, IUserMembership } from '@/shared-types/interfaces'
import { getCommunityBaseURL } from '@/src/libs/utils'

export function CommunitySwitcherContent({
  userMemberships,
  membership,
  onClose,
}: {
  userMemberships?: IUserMembership[]
  membership: IMembership | null
  onClose: () => void
}) {
  const [searchString, setSearchString] = useState('')
  const setMembership = useStore((state) => state.community.setMembership)

  const hasRealMemberships = (userMemberships?.length || 0) > 0

  const miniSearch = useMemo(() => {
    const ms = new MiniSearch({
      fields: ['name'],
      storeFields: ['name', 'id'],
      processTerm: (term: string) =>
        term
          .normalize('NFKD') // Decompose characters (á → a + ´)
          .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
          .toLowerCase()
          .trim(),
    })

    const documents =
      userMemberships?.map((um: IUserMembership) => ({
        id: um.membership.id,
        name: um.membership.name,
      })) || []

    ms.addAll(documents)
    return ms
  }, [userMemberships])

  const filteredMemberships = useMemo(() => {
    if (!searchString) {
      return userMemberships
        ?.filter(
          (um: IUserMembership) =>
            um.status === USER_MEMBERSHIP_STATUS_ENUM.accepted && um.membership.id !== membership?.id,
        )
        .sort((a: IUserMembership, b: IUserMembership) => a.membership.name.localeCompare(b.membership.name))
    }

    const results = miniSearch.search(searchString, {
      prefix: true,
      fuzzy: 0.2,
    })

    return results
      .map((result) => userMemberships.find((um: IUserMembership) => um.membership.id === result.id))
      .filter((um) => um && um.status === USER_MEMBERSHIP_STATUS_ENUM.accepted && um.membership.id !== membership?.id)
      .sort((a, b) => a.membership.name.localeCompare(b.membership.name))
  }, [searchString, userMemberships, membership?.id, miniSearch])

  const itemClassName =
    'h-[3.25rem] px-4 flex flex-nowrap items-center text-black-600 dark:text-white-200 hover:bg-white-100 dark:hover:bg-black-300 text-sm font-semibold leading-10 w-full'

  return (
    <div className="flex max-h-[calc(100%-4.5625rem)] flex-1 flex-col py-1.5 md:block">
      {hasRealMemberships && (
        <div className="px-4 py-1.5">
          <div className="relative">
            <StandardInput
              placeholder="Search"
              value={searchString}
              onChange={(event) => setSearchString(event.target.value)}
            />
          </div>
        </div>
      )}

      <Link href="/create-community" className={itemClassName}>
        <div className="mr-2 flex h-10 w-10 items-center justify-center rounded-base bg-grey-200 text-black-200 dark:bg-black-500 dark:text-black-100">
          <Plus16Icon />
        </div>
        Create a community
      </Link>

      {hasRealMemberships && (
        <ScrollArea
          className="flex-1 overflow-hidden md:flex-none md:overflow-auto"
          viewportClassName="md:max-h-[calc(50vh)] [&>div]:table-fixed [&>div]:w-full"
        >
          {filteredMemberships.map((um: IUserMembership) => (
            <Link
              key={um.membership.id}
              title={um.membership.name}
              className={itemClassName}
              href={getCommunityBaseURL(um.membership)}
              onClick={() => {
                onClose()
                setMembership(um.membership)
              }}
            >
              <Favicon
                className="mr-2 h-10 w-10"
                communityName={um.membership.name}
                src={um.membership.membership_setting?.favicon}
                width={40}
                height={40}
                variant="inverted"
              />
              <div className="max-w-full flex-1 truncate whitespace-nowrap">{um.membership.name}</div>
            </Link>
          ))}
        </ScrollArea>
      )}
    </div>
  )
}
