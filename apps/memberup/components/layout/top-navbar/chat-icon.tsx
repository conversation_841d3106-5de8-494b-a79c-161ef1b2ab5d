import { Chat20Icon } from '@/components/icons/20px/chat-20-icon'
import { Chat24Icon } from '@/components/icons/24px/chat-24-icon'
import { NotificationsIndicator } from '@/components/ui/notifications-indicator'
import { useStore } from '@/hooks/useStore'

export function ChatIcon({ small }: { small?: boolean }) {
  const totalUnread = useStore((state) => state.notifications.totalUnread)

  return (
    <div>
      <NotificationsIndicator count={totalUnread} small={small}>
        {small ? <Chat20Icon /> : <Chat24Icon />}
        <span className="sr-only">Chat</span>
      </NotificationsIndicator>
    </div>
  )
}
