import { useKnockFeed } from '@knocklabs/react'
import { useEffect } from 'react'

import { Notifications20Icon } from '@/components/icons/20px/notifications-20-icon'
import { Bell24Icon } from '@/components/icons/24px/bell-24-icon'
import { NotificationsIndicator } from '@/components/ui/notifications-indicator'

export function NotificationsIcon({ small }: { small?: boolean }) {
  const { feedClient, useFeedStore } = useKnockFeed()
  const { metadata } = useFeedStore()

  // Force fetch notifications from the feed
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        await feedClient.fetch()
      } catch (error) {
        // Just ignore errors
      }
    }

    if (feedClient) {
      fetchNotifications()
    }
  }, [feedClient])

  return (
    <NotificationsIndicator count={metadata?.unread_count || 0} small={small}>
      {small ? <Notifications20Icon /> : <Bell24Icon />}
    </NotificationsIndicator>
  )
}
