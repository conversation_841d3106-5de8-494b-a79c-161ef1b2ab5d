import { useKnockFeed } from '@knocklabs/react'

import { ProfilePicture } from '@/components/images/profile-picture'
import { NotificationsIndicator } from '@/components/ui/notifications-indicator'
import { useStore } from '@/hooks/useStore'
import { getFullName } from '@/lib/formatting'
import { cn } from '@/lib/utils'
import { IUser, IUserProfile } from '@/shared-types/interfaces'

interface NotificationsProfileProps {
  user: IUser
  profile: IUserProfile
}

export function NotificationsProfile({ user, profile }: NotificationsProfileProps) {
  const { useFeedStore } = useKnockFeed()
  const { metadata } = useFeedStore()
  const totalUnreadInboxMessages = useStore((state) => state.notifications.totalUnread)

  const totalUnread = (metadata?.unread_count || 0) + totalUnreadInboxMessages

  return (
    <NotificationsIndicator count={totalUnread} profile>
      <ProfilePicture
        className={cn('h-8 w-8 shrink-0 rounded-full')}
        src={profile.image}
        cropArea={profile.image_crop_area}
        alt={getFullName(user)}
        height={32}
        width={32}
      />
    </NotificationsIndicator>
  )
}
