'use client'

import { Dot<PERSON><PERSON>ieReact } from '@lottiefiles/dotlottie-react'
import { AnimatePresence, motion } from 'framer-motion'
import { usePathname } from 'next/navigation'
import { useEffect, useRef } from 'react'

import { AuthModal } from '@/components/layout/auth-modal'
import { LeftNavbar } from '@/components/layout/left-navbar'
import { TopNavbar } from '@/components/layout/top-navbar'
import { KnockFeedSetup } from '@/components/notifications/knock-feed-setup'
import { ScrollArea, ScrollBar } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useIsFullscreenLayout } from '@/hooks/useIsFullscreenLayout'
import { useMountedRef } from '@/hooks/useMountedRef'
import { useStore } from '@/hooks/useStore'
import { isCommunityPath } from '@/lib/communities'
import { communityFetchError } from '@/lib/error-messages'
import Search from '@/memberup/components/dialogs/search'
import { setFeedTrackIds } from '@/memberup/store/features/feedTrackSlice'
import { useAppDispatch } from '@/memberup/store/hooks'
import { getFeedTracks } from '@/shared-services/apis/feed-track.api'
import { getMembershipApi } from '@/shared-services/apis/membership.api'
import { IMembership } from '@/shared-types/interfaces'

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const dispatch = useAppDispatch()
  const user = useStore((state) => state.auth.user)
  const knockToken = useStore((state) => state.auth.knockToken)
  const scrollAreaRef = useRef(null)
  const previousScrollPositionRef = useRef<number>(0)
  const breakpointRef = useRef(null)
  const navbarOffset = useStore((state) => state.ui.navbarOffset)
  const setNavbarOffset = useStore((state) => state.ui.setNavbarOffset)
  const membership = useStore((state) => state.community.membership)
  const navbarOffsetRef = useRef(navbarOffset)
  const newlyCreatedId = useStore((state) => state.community.newlyCreatedId)
  const newlyCreatedTimeoutRef = useRef(null)
  const setLoadingCommunity = useStore((state) => state.community.setLoadingCommunity)
  const resetCommunityState = useStore((state) => state.community.reset)
  const setNewlyCreatedId = useStore((state) => state.community.setNewlyCreatedId)
  const setMembership = useStore((state) => state.community.setMembership)
  const setStickyItemsOffset = useStore((state) => state.ui.setStickyItemsOffset)
  const stickyItemsOffset = useStore((state) => state.ui.stickyItemsOffset)
  const topNavbarRef = useRef(null)
  const membershipSlugRef = useRef(membership?.slug)
  const membershipIdRef = useRef(membership?.id)
  const fullscreenLayout = useIsFullscreenLayout()
  const setSearchOpen = useStore((state) => state.ui.setSearchOpen)
  const searchOpen = useStore((state) => state.ui.searchOpen)
  const mountedRef = useMountedRef()

  useEffect(() => {
    async function fetchFeedTracks() {
      if (user) {
        const feedTracksMap = await getFeedTracks()
        dispatch(setFeedTrackIds(feedTracksMap))
      }
    }
    fetchFeedTracks()
  }, [dispatch, user])

  const calculateNavbarOffset = () => {
    const scrollArea = scrollAreaRef.current
    const scrollTop = scrollArea.scrollTop
    const topNavbarHeight = topNavbarRef.current.getBoundingClientRect().height
    const maxOffset = topNavbarHeight
    let newOffset = null
    let newStickyItemsOffset = 20

    if (previousScrollPositionRef.current < scrollTop && navbarOffset < maxOffset + 20) {
      newOffset = Math.min(maxOffset, navbarOffset + scrollTop - previousScrollPositionRef.current)
      newStickyItemsOffset = Math.max(20, stickyItemsOffset - (scrollTop - previousScrollPositionRef.current))
    } else if (scrollTop < previousScrollPositionRef.current) {
      if (navbarOffset > 0) {
        newOffset = Math.max(0, navbarOffset - (previousScrollPositionRef.current - scrollTop))
      }
      newStickyItemsOffset = Math.min(
        maxOffset + 20,
        stickyItemsOffset + (previousScrollPositionRef.current - scrollTop),
      )
    }

    if (newOffset !== null) {
      setNavbarOffset(newOffset)
      navbarOffsetRef.current = newOffset
    }

    setStickyItemsOffset(newStickyItemsOffset)
    previousScrollPositionRef.current = scrollTop
  }

  const onScroll = () => {
    if (fullscreenLayout) return

    calculateNavbarOffset()
  }

  useEffect(() => {
    if (fullscreenLayout) return

    topNavbarRef.current = document.getElementById('top-navbar')
  }, [fullscreenLayout])

  useEffect(() => {
    // Only runs on initial render
    topNavbarRef.current = document.getElementById('top-navbar')

    const getBreakpoint = (width: number) => {
      if (width < 768) {
        return 'mobile'
      } else if (width < 1280) {
        return 'md'
      } else {
        return 'xl'
      }
    }

    breakpointRef.current = getBreakpoint(window.innerWidth)

    const NAVBAR_TARGET_HEIGHT = {
      mobile: 97,
      md: 114,
      xl: 58,
    }

    const STICKY_MARGIN = 20

    const windowResizeHandler = () => {
      const oldBreakpoint = breakpointRef.current
      const newBreakpoint = getBreakpoint(window.innerWidth)

      if (newBreakpoint !== oldBreakpoint) {
        const currentActualNavbarOffset = Math.round(navbarOffsetRef.current)
        const oldNavbarAttemptedFullHeight = NAVBAR_TARGET_HEIGHT[oldBreakpoint as keyof typeof NAVBAR_TARGET_HEIGHT]
        const newNavbarTargetFullHeight = NAVBAR_TARGET_HEIGHT[newBreakpoint as keyof typeof NAVBAR_TARGET_HEIGHT]

        // Check if the navbar was fully visible (or intended to be) at the old breakpoint
        const wasNavbarEffectivelyFullAtOldBreakpoint = currentActualNavbarOffset === oldNavbarAttemptedFullHeight

        if (wasNavbarEffectivelyFullAtOldBreakpoint) {
          // Navbar was full, so keep it full at the new breakpoint's height
          setNavbarOffset(newNavbarTargetFullHeight)
          navbarOffsetRef.current = newNavbarTargetFullHeight
          setStickyItemsOffset(newNavbarTargetFullHeight + STICKY_MARGIN)
        } else {
          // Navbar was hidden or partially scrolled, so set it to hidden at the new breakpoint
          setNavbarOffset(0)
          navbarOffsetRef.current = 0
          setStickyItemsOffset(STICKY_MARGIN)
        }

        breakpointRef.current = newBreakpoint
      }
    }

    window.addEventListener('resize', windowResizeHandler)

    return () => {
      window.removeEventListener('resize', windowResizeHandler)
    }
  }, [setNavbarOffset, setStickyItemsOffset])

  useEffect(() => {
    if (newlyCreatedId && newlyCreatedId === membership?.id && newlyCreatedTimeoutRef.current === null) {
      newlyCreatedTimeoutRef.current = setTimeout(() => {
        setNewlyCreatedId(null)
        newlyCreatedTimeoutRef.current = null
      }, 4000)
    }

    return () => {
      if (newlyCreatedTimeoutRef.current !== null) {
        clearTimeout(newlyCreatedTimeoutRef.current)
        newlyCreatedTimeoutRef.current = null
      }
    }
  }, [membership, newlyCreatedId, setNewlyCreatedId])

  // Update the refs whenever membership changes
  useEffect(() => {
    membershipSlugRef.current = membership?.slug
    membershipIdRef.current = membership?.id
  }, [membership?.slug, membership?.id])

  useEffect(() => {
    if (isCommunityPath(pathname)) {
      const pathnameParts = pathname.split('/')
      const communitySlug = pathnameParts[1]

      if (communitySlug) {
        // First check if membership exists in user's user_memberships
        const userMembership = user?.user_memberships?.find((um) => um.membership.slug === communitySlug)

        if (userMembership) {
          // Set membership from user_membership data
          setMembership(userMembership.membership as IMembership)
        } else if (membershipSlugRef.current !== communitySlug) {
          // Only fetch from server if it's a new community we haven't loaded before
          const fetchMembership = async () => {
            try {
              setLoadingCommunity(true)
              const res = await getMembershipApi({ slug: communitySlug })

              const fetchedMembership = res?.data?.data?.membership

              if (
                mountedRef.current &&
                res.data.success !== false &&
                fetchedMembership &&
                fetchedMembership.slug === communitySlug
              ) {
                setMembership(fetchedMembership)
                setLoadingCommunity(false)
              }
            } catch {
              toast.error(communityFetchError)
              setLoadingCommunity(false)
            }
          }

          fetchMembership()
        }
      }
    } else if (membershipIdRef.current) {
      resetCommunityState()
    }
  }, [pathname, resetCommunityState, user?.user_memberships, setMembership, mountedRef, setLoadingCommunity])

  if (fullscreenLayout) return children

  return (
    <div>
      {user?.id && knockToken && <KnockFeedSetup />}
      <div className="app-layout absolute left-0 top-0 h-full w-full">
        <ScrollArea
          className="absolute left-0 top-0 h-full w-full"
          id="app-scroll-area"
          viewportId="app-scroll-area-viewport"
          ref={scrollAreaRef}
          onScroll={onScroll}
        >
          <div className="relative z-0 flex w-full flex-col items-center">
            <TopNavbar height={navbarOffset} />
            <div className="content-container padded-content-container transition-duration-400 relative z-0 flex pt-[7.3125rem] transition-[padding-top] md:pt-[9.1875rem] xl:pt-[5.125rem]">
              <LeftNavbar />
              <div className="grow">{children}</div>
            </div>
          </div>
          <ScrollBar orientation="vertical" />
        </ScrollArea>
      </div>
      <AuthModal />
      <AnimatePresence>
        {newlyCreatedId && (
          <motion.div
            className="fixed left-0 top-0 z-[10000] flex h-full w-full items-center justify-center bg-white-500 dark:bg-black-700"
            initial={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <DotLottieReact
              src="/img/community-created-animation.lottie"
              autoplay
              className="h-auto w-full max-w-[700px]"
            />
          </motion.div>
        )}
      </AnimatePresence>
      <Search open={searchOpen} onClose={() => setSearchOpen(false)} />
    </div>
  )
}
