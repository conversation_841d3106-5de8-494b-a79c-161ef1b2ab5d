import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { useParams, usePathname } from 'next/navigation'

import { HorizontalNavigationMenu } from './HorizontalNavigationMenu'
import { HorizontalNavigationMenuItem } from './HorizontalNavigationMenuItem'
import { RedNotification12Icon } from '@/components/icons/12px/red-notification-12-icon'
import { RedNotification16Icon } from '@/components/icons/16px/red-notification-16-icon'
import { AdminSettings } from '@/components/settings/admin-settings/AdminSettings'
import { MemberSettings } from '@/components/settings/member-settings'
import { useMemberRequests } from '@/hooks/useMemberRequests'
import { useStore } from '@/hooks/useStore'

export function CommunityHorizontalNavigationMenu() {
  const params = useParams()
  const pathname = usePathname()
  const { hasMemberRequests } = useMemberRequests()

  const menuItems = [
    { label: 'Community', href: `/${params.slug}` },
    { label: 'Content', href: `/${params.slug}/content` },
    { label: 'Events', href: `/${params.slug}/events` },
    { label: 'Members', href: `/${params.slug}/members` },
    { label: 'About', href: `/${params.slug}/about` },
  ]

  const selectedIndex = menuItems.findIndex((item) => pathname === item.href)
  const user = useStore((state) => state.auth.user)
  const userCanManageCommunity = useStore((state) => state.community.userCanManageCommunity)

  const setSettingsModalOpen = useStore((state) => state.ui.setSettingsModalOpen)
  const settingsModalOpen = useStore((state) => state.ui.settingsModalOpen)

  return (
    <>
      <HorizontalNavigationMenu selectedIndex={selectedIndex}>
        {menuItems.map((item) => (
          <HorizontalNavigationMenuItem key={item.href} href={item.href}>
            {item.label}
            {item.label === 'Members' && hasMemberRequests && (
              <>
                <RedNotification12Icon className="ml-4 text-red-200 sm:hidden" aria-label="Pending member requests" />
                <RedNotification16Icon
                  className="ml-4 hidden text-red-200 sm:block"
                  aria-label="Pending member requests"
                />
              </>
            )}
          </HorizontalNavigationMenuItem>
        ))}
        {user && (
          <HorizontalNavigationMenuItem onClick={() => setSettingsModalOpen(true)}>
            Settings
          </HorizontalNavigationMenuItem>
        )}
      </HorizontalNavigationMenu>
      <VisuallyHidden>
        {userCanManageCommunity ? (
          <AdminSettings
            open={settingsModalOpen}
            onOpenChange={(value: boolean) => setSettingsModalOpen(value ?? false)}
          />
        ) : (
          <MemberSettings
            open={settingsModalOpen}
            onOpenChange={(value: boolean) => setSettingsModalOpen(value ?? false)}
          />
        )}
      </VisuallyHidden>
    </>
  )
}
