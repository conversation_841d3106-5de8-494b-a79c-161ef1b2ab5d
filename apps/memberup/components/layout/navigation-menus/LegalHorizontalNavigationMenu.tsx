import { usePathname } from 'next/navigation'

import { HorizontalNavigationMenu } from './HorizontalNavigationMenu'
import { HorizontalNavigationMenuItem } from './HorizontalNavigationMenuItem'
import { legalLinks } from '@/lib/constants'

export function LegalHorizontalNavigationMenu() {
  const pathname = usePathname()
  const selectedIndex = legalLinks.findIndex((link) => pathname === link.href)

  return (
    <HorizontalNavigationMenu selectedIndex={selectedIndex}>
      {legalLinks.map((link) => (
        <HorizontalNavigationMenuItem key={link.href} href={link.href}>
          {link.label}
        </HorizontalNavigationMenuItem>
      ))}
    </HorizontalNavigationMenu>
  )
}
