import dynamic from 'next/dynamic'

const Amex = dynamic(() => import('react-payment-logos/dist/flat-rounded/Amex'))
const Diners = dynamic(() => import('react-payment-logos/dist/flat-rounded/Diners'))
const Discover = dynamic(() => import('react-payment-logos/dist/flat-rounded/Discover'))
const Generic = dynamic(() => import('react-payment-logos/dist/flat-rounded/Generic'))
const Jcb = dynamic(() => import('react-payment-logos/dist/flat-rounded/Jcb'))
const Maestro = dynamic(() => import('react-payment-logos/dist/flat-rounded/Maestro'))
const Mastercard = dynamic(() => import('react-payment-logos/dist/flat-rounded/Mastercard'))
const Unionpay = dynamic(() => import('react-payment-logos/dist/flat-rounded/Unionpay'))
const Visa = dynamic(() => import('react-payment-logos/dist/flat-rounded/Visa'))

export function DynamicCardIcon({ card, className }: { card: string; className?: string }) {
  switch (card) {
    case 'mastercard':
      return <Mastercard className={className} />
    case 'visa':
      return <Visa className={className} />
    case 'amex':
      return <Amex className={className} />
    case 'diners':
      return <Diners className={className} />
    case 'discover':
      return <Discover className={className} />
    case 'jcb':
      return <Jcb className={className} />
    case 'maestro':
      return <Maestro className={className} />
    case 'unionpay':
      return <Unionpay className={className} />
    default:
      return <Generic className={className} />
  }
}
