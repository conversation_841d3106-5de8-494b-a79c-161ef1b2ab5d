import { type SVGProps } from 'react'

export const Chat20Icon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
      <path
        d="M9.99757 10.3178C10.1729 10.3178 10.3151 10.1756 10.3151 10.0002C10.3151 9.82482 10.1729 9.68262 9.99757 9.68262"
        stroke="currentColor"
        strokeWidth="1.63623"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.99747 10.3178C9.82211 10.3178 9.67995 10.1756 9.67995 10.0002C9.67995 9.82482 9.82211 9.68262 9.99747 9.68262"
        stroke="currentColor"
        strokeWidth="1.63623"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.4903 10.3178C13.6656 10.3178 13.8078 10.1756 13.8078 10.0002C13.8078 9.82482 13.6656 9.68262 13.4903 9.68262"
        stroke="currentColor"
        strokeWidth="1.63623"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.4902 10.3178C13.3148 10.3178 13.1727 10.1756 13.1727 10.0002C13.1727 9.82482 13.3148 9.68262 13.4902 9.68262"
        stroke="currentColor"
        strokeWidth="1.63623"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.50505 10.3178C6.68041 10.3178 6.82257 10.1756 6.82257 10.0002C6.82257 9.82482 6.68041 9.68262 6.50505 9.68262"
        stroke="currentColor"
        strokeWidth="1.63623"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.50504 10.3178C6.32968 10.3178 6.18745 10.1756 6.18745 10.0002C6.18745 9.82482 6.32968 9.68262 6.50504 9.68262"
        stroke="currentColor"
        strokeWidth="1.63623"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.0024 1.81885C11.4818 1.81931 12.9334 2.22116 14.2026 2.98159C15.4717 3.74203 16.5109 4.83257 17.2095 6.13707C17.908 7.44157 18.2397 8.91117 18.1693 10.3893C18.0989 11.8675 17.629 13.2989 16.8096 14.531L18.1812 18.1812L13.5885 17.3504C12.4826 17.891 11.269 18.1749 10.0381 18.181C8.80722 18.1871 7.59085 17.9152 6.47965 17.3856C5.36847 16.8561 4.39102 16.0824 3.62046 15.1223C2.84978 14.1623 2.30555 13.0407 2.02867 11.841C1.75166 10.6413 1.74887 9.3946 2.02067 8.19375C2.29246 6.99288 2.83161 5.86886 3.59811 4.90548C4.3646 3.94209 5.33862 3.16419 6.44748 2.62975C7.55636 2.09531 8.77153 1.81812 10.0024 1.81885Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
