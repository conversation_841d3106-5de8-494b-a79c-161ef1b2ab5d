import { type SVGProps } from 'react'

export const Notifications20Icon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      version="1.1"
      id="svg6"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <defs id="defs10" />
      <g id="g295" transform="translate(-0.70035,-2.5e-4)">
        <path
          d="M 9.11841,18.7007 H 12.2822"
          stroke="currentColor"
          strokeWidth="2.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          id="path2"
        />
        <path
          d="m 17.0279,7.35223 c 0,-1.60521 -0.6666,-3.14466 -1.8533,-4.27972 C 13.988,1.93747 12.3785,1.2998 10.7004,1.2998 9.02218,1.2998 7.41276,1.93747 6.22611,3.07251 5.03948,4.20757 4.37282,5.74702 4.37282,7.35223 v 5.29587 c 0,0.6019 -0.25,1.1793 -0.69498,1.6049 C 3.23286,14.6787 2.6293,14.9178 2,14.9178 h 17.4007 c -0.6293,0 -1.2329,-0.2391 -1.6779,-0.6648 -0.445,-0.4256 -0.6949,-1.003 -0.6949,-1.6049 z"
          stroke="currentColor"
          strokeWidth="2.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          id="path4"
        />
      </g>
    </svg>
  )
}
