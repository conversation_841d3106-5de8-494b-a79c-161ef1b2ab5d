import { SVGProps } from 'react'

export function Chat24Icon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g transform="translate(-0.27665556,0.12550816)">
        <path
          d="m 12.2738,12.2559 c 0.2104,0 0.381,-0.1706 0.381,-0.3811 0,-0.2105 -0.1706,-0.3811 -0.381,-0.3811"
          stroke="currentColor"
          strokeWidth="1.63623"
          strokeLinecap="round"
          strokeLinejoin="round"
          id="path1"
        />
        <path
          d="m 12.2736,12.2554 c -0.2104,0 -0.381,-0.1706 -0.381,-0.3811 0,-0.2105 0.1706,-0.3811 0.381,-0.3811"
          stroke="currentColor"
          strokeWidth="1.63623"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="m 16.465,12.2559 c 0.2104,0 0.381,-0.1706 0.381,-0.3811 0,-0.2105 -0.1706,-0.3811 -0.381,-0.3811"
          stroke="currentColor"
          strokeWidth="1.63623"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="m 16.4649,12.2554 c -0.2104,0 -0.381,-0.1706 -0.381,-0.3811 0,-0.2105 0.1706,-0.3811 0.381,-0.3811"
          stroke="currentColor"
          strokeWidth="1.63623"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="m 8.08272,12.2559 c 0.21043,0 0.38103,-0.1706 0.38103,-0.3811 0,-0.2105 -0.1706,-0.3811 -0.38103,-0.3811"
          stroke="currentColor"
          strokeWidth="1.63623"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="m 8.08264,12.2554 c -0.21043,0 -0.3811,-0.1706 -0.3811,-0.3811 0,-0.2105 0.17067,-0.3811 0.3811,-0.3811"
          stroke="currentColor"
          strokeWidth="1.63623"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="m 12.2796,2.05713 c 1.7753,5.5e-4 3.5172,0.48277 5.0402,1.39529 1.523,0.91253 2.77,2.22118 3.6082,3.78657 0.8383,1.5654 1.2364,3.32891 1.1519,5.10271 -0.0845,1.7738 -0.6484,3.4915 -1.6317,4.97 l 1.6459,4.3802 -5.5113,-0.9969 c -1.327,0.6487 -2.7834,0.9894 -4.2604,0.9967 C 10.8453,21.6991 9.38569,21.3728 8.05225,20.7372 6.71883,20.1018 5.54589,19.1734 4.62122,18.0213 3.6964,16.8693 3.04333,15.5233 2.71107,14.0837 2.37867,12.6441 2.37532,11.148 2.70147,9.70701 3.02763,8.26597 3.67461,6.91715 4.5944,5.76109 5.51419,4.60502 6.68302,3.67154 8.01364,3.03022 9.3443,2.38888 10.8025,2.05626 12.2796,2.05713 Z"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          id="path7"
        />
      </g>
    </svg>
  )
}
