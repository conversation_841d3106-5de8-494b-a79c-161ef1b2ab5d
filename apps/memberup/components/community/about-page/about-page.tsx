'use client'

import dynamic from 'next/dynamic'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'

import { EditButton } from './edit-button'
import { JoinCommunityButton } from '@/components/community/join-community-button'
import { StaticGallery } from '@/components/community/static-gallery/static-gallery'
import { Info24Icon } from '@/components/icons'
import { PublicCommunity12Icon } from '@/components/icons/12px/PublicCommunity12Icon'
import { Lock20Icon } from '@/components/icons/20px/Lock20Icon'
import { Tag20Icon } from '@/components/icons/20px/tag-20-icon'
import { Group24Icon } from '@/components/icons/24px/group-24-icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button } from '@/components/ui'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useMountedRef } from '@/hooks/useMountedRef'
import { useStore } from '@/hooks/useStore'
import { formatThousands } from '@/lib/formatting'
import defaultAboutImage from '@/public/img/cover-photo-placeholder.jpg'
import { getCommunityPricing, isPublicCommunity } from '@/shared-libs/membership-settings'
import { getMemberFullName } from '@/shared-libs/profile'
import { getMembershipStatsApi } from '@/shared-services/apis/membership.api'
import { getMuxAssetApi, getMuxUploadApi } from '@/shared-services/apis/mux.api'

type MuxAssetStatus = 'in-progress' | 'done' | 'queued'

interface MuxAssetPollingEntry {
  interval: ReturnType<typeof setInterval>
  status: MuxAssetStatus
}

interface MuxAssetPollingMap {
  [assetId: string]: MuxAssetPollingEntry
}

const EditableCommunityAboutPage = dynamic(() => import('./editable-about-page'))

export default function CommunityAboutPage() {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership.membership_setting)
  const owner = useStore((state) => state.community.membership.owner)
  const [membersCount, setMembersCount] = useState(0)
  const [isEditing, setIsEditing] = useState(false)
  const [aboutTitle, setAboutTitle] = useState(membershipSetting.about_title ?? membership.name)
  const userCanManageCommunity = useStore((state) => state.community.userCanManageCommunity)

  useEffect(() => {
    setAboutTitle(membershipSetting.about_title ?? membership.name)
  }, [membershipSetting.about_title, membership.name])

  const galleryMedia = membershipSetting?.about_gallery as any[]
  const [tempGalleryVideoData, setTempGalleryVideoData] = useState({})
  const hasGallery = galleryMedia && galleryMedia.length > 0

  const muxUploadPolling = useRef<MuxAssetPollingMap>({})
  const mountedRef = useMountedRef()

  useEffect(() => {
    if (!membership) {
      return
    }
    async function initialize() {
      const stats = await getMembershipStatsApi(membership.id)
      setMembersCount(stats.data.totalMembers)
    }
    initialize()
  }, [membership])

  const startUploadPolling = async (uploadId: any) => {
    if (!(muxUploadPolling.current[uploadId] === undefined || muxUploadPolling.current[uploadId] === null)) return

    muxUploadPolling.current[uploadId] = {
      interval: null,
      status: 'queued',
    }

    const pollMuxUpload = async () => {
      if (['in-progress', 'done'].includes(muxUploadPolling.current[uploadId]?.status)) return

      muxUploadPolling.current[uploadId].status = 'in-progress'

      const res = await getMuxUploadApi(uploadId)
      if (!mountedRef.current) return

      const assetId = res?.data?.data?.asset_id
      if (!assetId) muxUploadPolling.current[uploadId].status = 'queued'

      const {
        data: { data: assetData },
      } = await getMuxAssetApi(assetId)
      if (!mountedRef.current) return

      if (assetData?.playback_ids?.[0]?.id) {
        muxUploadPolling.current[uploadId].status = 'done'

        clearInterval(muxUploadPolling.current[uploadId].interval)
        setTempGalleryVideoData((tempData) => ({
          ...tempData,
          [uploadId]: assetData,
        }))
      }
    }

    muxUploadPolling.current[uploadId].interval = setInterval(async () => {
      pollMuxUpload()
    }, 5000)

    pollMuxUpload()
  }

  useEffect(() => {
    galleryMedia?.forEach((item) => {
      if (item?.mux_upload_id && !item.mux_asset?.playback_ids?.[0]?.id) {
        startUploadPolling(item.mux_upload_id)
      }
    })
  }, [galleryMedia])

  const getAssetData = (item: any) => {
    if (!item?.mux_upload_id) return

    if (item?.mux_asset?.playback_ids?.[0]?.id) {
      return item.mux_asset
    }
    if (tempGalleryVideoData[item.mux_upload_id]) {
      return tempGalleryVideoData[item?.mux_upload_id]
    }
  }

  return (
    <>
      {isEditing && userCanManageCommunity ? (
        <EditableCommunityAboutPage
          closeEditMode={() => setIsEditing(false)}
          tempGalleryVideoData={tempGalleryVideoData}
          setTempGalleryVideoData={setTempGalleryVideoData}
        />
      ) : (
        <div className="relative h-dvh w-full rounded-none bg-white-500 pb-[6.25rem] dark:bg-black-500 md:h-full md:rounded-base md:pb-0">
          <div className={`flex w-full justify-between px-5 pt-6 md:pt-5 ${!hasGallery ? 'pb-5' : ''}`}>
            <h3 className="text-xl font-semibold leading-6">{aboutTitle}</h3>
            {userCanManageCommunity && <EditButton onClick={() => setIsEditing(true)} />}
          </div>

          {hasGallery ? (
            <div className="p-5">
              <StaticGallery getAssetData={getAssetData} media={galleryMedia} />
            </div>
          ) : (
            <div className="px-5 pb-5">
              <Image
                className="h-full max-h-full w-full select-none rounded-base object-cover"
                src={defaultAboutImage.src}
                alt="default about image"
                width={722}
                height={406}
              />
            </div>
          )}

          <div className="inline-flex w-full flex-wrap items-center justify-start gap-4 px-5 py-[0.625rem] text-xs font-semibold text-black-700 dark:border-grey-900 dark:text-white-500 md:flex-nowrap md:gap-10">
            <div className="flex items-center gap-1">
              {isPublicCommunity(membershipSetting) ? (
                <PublicCommunity12Icon className="inline h-5 w-5 shrink-0 align-middle" />
              ) : (
                <Lock20Icon className="inline h-5 w-5 shrink-0 align-middle" />
              )}
              {isPublicCommunity(membershipSetting) ? 'Public' : 'Private'}
            </div>
            <div className="flex items-center gap-1">
              <div className="relative h-5 w-[1.375rem] overflow-hidden">
                <Group24Icon className="absolute -top-0.5 h-[1.375rem] w-[1.375rem]" />
              </div>
              {membersCount && (
                <div>
                  {formatThousands(membersCount)} {membersCount !== 1 ? 'Members' : 'Member'}
                </div>
              )}
            </div>
            <div className="flex items-center gap-1">
              <div className="h-5 w-5">
                <Tag20Icon />
              </div>
              <div>{getCommunityPricing(membershipSetting) ? 'Paid' : 'Free'}</div>
            </div>
            {owner && (
              <div className="flex items-center gap-1">
                <div>
                  <ProfilePicture
                    className="h-6 w-6"
                    src={owner.profile.image}
                    cropArea={owner.profile.image_crop_area}
                    alt={getMemberFullName(owner)}
                    height={24}
                    width={24}
                  />
                </div>
                <div>{getMemberFullName(owner)}</div>
              </div>
            )}
          </div>

          {membershipSetting.about_text ? (
            <div
              className="rendered-editor-text px-5 py-6 text-sm text-black-600 dark:text-white-200"
              dangerouslySetInnerHTML={{
                __html: membershipSetting.about_text,
              }}
            />
          ) : (
            <div
              className={`rendered-editor-text ${userCanManageCommunity ? 'p-5' : 'pb-5'} text-sm text-black-600 dark:text-white-200`}
            >
              {userCanManageCommunity ? 'Add your description...' : ''}
            </div>
          )}

          <Link
            href="/privacy-policy"
            target="_blank"
            rel="noopener noreferrer"
            className="mx-5 block border-t border-grey-900 py-5 text-sm text-black-200 dark:text-black-100 md:hidden"
          >
            Privacy and terms
          </Link>

          <div
            className={`fixed bottom-0 left-0 right-0 z-30 border-t border-grey-900 bg-white-500 px-5 pb-5 dark:bg-black-500 md:hidden`}
          >
            {!userCanManageCommunity ? (
              <JoinCommunityButton />
            ) : (
              <Popover>
                <PopoverTrigger asChild>
                  <div className="w-full">
                    <Button className="mt-4 w-full" variant="community-primary" disabled>
                      Join Community
                    </Button>
                  </div>
                </PopoverTrigger>
                <PopoverContent>
                  <div className="flex items-center justify-center gap-2 p-4">
                    <Info24Icon className="shrink-0" />
                    <p className="text-sm">Preview of the button people click to join.</p>
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>
      )}
    </>
  )
}
