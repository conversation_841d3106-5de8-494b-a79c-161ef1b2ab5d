'use client'

import { cloneDeep, isEqual } from 'lodash'
import { useEffect, useRef, useState } from 'react'

import { EditableGallery } from '@/components/community/editable-gallery'
import { PublicCommunity12Icon } from '@/components/icons/12px/PublicCommunity12Icon'
import { Lock20Icon } from '@/components/icons/20px/Lock20Icon'
import { Tag20Icon } from '@/components/icons/20px/tag-20-icon'
import { Group24Icon } from '@/components/icons/24px/group-24-icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button, Input } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import TextEditor from '@/components/ui/tiptap/text-editor'
import { useMountedRef } from '@/hooks/useMountedRef'
import useMultipleFileUploads, { IUploadFile } from '@/hooks/useMultipleFileUploads'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { formatThousands } from '@/lib/formatting'
import { editorStringsEqual } from '@/lib/parsing'
import { getCommunityPricing, isPublicCommunity } from '@/shared-libs/membership-settings'
import { getMemberFullName } from '@/shared-libs/profile'
import { getMembershipStatsApi, updateMembershipApi } from '@/shared-services/apis/membership.api'
import { getMuxAssetApi, getMuxUploadApi } from '@/shared-services/apis/mux.api'

type MuxAssetStatus = 'in-progress' | 'done' | 'queued'

interface MuxAssetPollingEntry {
  interval: ReturnType<typeof setInterval>
  status: MuxAssetStatus
}

interface MuxAssetPollingMap {
  [assetId: string]: MuxAssetPollingEntry
}

interface EditableCommunityAboutPageProps {
  closeEditMode: () => void
  tempGalleryVideoData: Record<string, any>
  setTempGalleryVideoData: (data: Record<string, any> | ((prev: Record<string, any>) => Record<string, any>)) => void
}

export default function EditableCommunityAboutPage({
  closeEditMode,
  tempGalleryVideoData,
  setTempGalleryVideoData,
}: EditableCommunityAboutPageProps) {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership.membership_setting)
  const setMembership = useStore((state) => state.community.setMembership)
  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)
  const [membersCount, setMembersCount] = useState(0)
  const [discardChangesWarningOpen, setDiscardChangesWarningOpen] = useState(false)
  const [originalAboutText, setOriginalAboutText] = useState(membershipSetting.about_text)
  const [aboutText, setAboutText] = useState(membershipSetting.about_text)
  const [aboutTitle, setAboutTitle] = useState(membershipSetting.about_title ?? membership.name)
  const [hasTitleChanges, setHasTitleChanges] = useState(false)
  const hasAboutTextChanges = !editorStringsEqual(originalAboutText, aboutText)
  const [editingAboutText, setEditingAboutText] = useState(false)

  const [galleryMedia, setGalleryMedia] = useState(() => {
    const initialItems = membershipSetting?.about_gallery ? [...(membershipSetting.about_gallery as any[])] : []

    if (initialItems.length < 7) {
      initialItems.push({
        id: 'uploader',
      })
    }

    return initialItems
  })

  const initialGalleryMediaRef = useRef(galleryMedia)
  const [saving, setSaving] = useState(false)
  const [savingCommunityData, setSavingCommunityData] = useState(false)
  const hasGalleryChanges = !isEqual(galleryMedia, initialGalleryMediaRef.current)
  const hasChanges = hasGalleryChanges || hasAboutTextChanges || hasTitleChanges
  const muxUploadPolling = useRef<MuxAssetPollingMap>({})
  const mountedRef = useMountedRef()
  const titleMaxLength = 60

  const { cancel, fileUploads, uploadFiles } = useMultipleFileUploads('community-about-page', membership.id)

  useEffect(() => {
    if (!membership) {
      return
    }
    async function initialize() {
      const stats = await getMembershipStatsApi(membership.id)
      setMembersCount(stats.data.totalMembers)
    }
    initialize()
  }, [membership])

  const handleSave = async () => {
    setSaving(true)
    setSavingCommunityData(false)
    setEditingAboutText(false)

    try {
      const updateData: any = {}
      let updatedGalleryMedia = null

      if (hasGalleryChanges) {
        try {
          const uploadData = await uploadFiles(galleryMedia.filter((item) => item.draft))
          if (!mountedRef.current) return

          if (!uploadData || uploadData.some((ud) => ud === null)) return // Upload was interrupted

          updatedGalleryMedia = galleryMedia.map((item) => {
            if (item.draft) {
              const newItem = { ...item }

              delete newItem.draft
              delete newItem.file

              return {
                ...newItem,
                ...uploadData.find((upload) => upload.id === item.id),
              }
            }

            return item
          })
        } catch (error) {
          if (error !== 'UPLOAD_FAILED') {
            throw error
          }
          toast.error(formSubmitError)
          setSaving(false)
        }
      }

      if (hasAboutTextChanges) {
        updateData.about_text = aboutText
      }

      if (hasTitleChanges) {
        updateData.about_title = aboutTitle
      }

      if (updatedGalleryMedia) {
        updateData.about_gallery = updatedGalleryMedia
          .filter((item: IUploadFile) => item.id !== 'uploader')
          .map((item: IUploadFile) => {
            if (item?.url?.startsWith('data:image')) {
              const newItem = { ...item }
              delete newItem.url
              return newItem
            }

            return item
          })
      }

      setSavingCommunityData(true)
      const result = await updateMembershipApi(updateData, membership.id)
      if (!mountedRef.current) return

      if (updatedGalleryMedia) {
        setGalleryMedia(updatedGalleryMedia)
        initialGalleryMediaRef.current = cloneDeep(updatedGalleryMedia)
      }

      const updatedMembership = result.data.data.membership
      const updatedUserMembership = result.data.data.user_membership

      setMembership(updatedMembership)
      updateUserMembership(updatedUserMembership)

      // dispatch(
      //     updateMembershipSettingSuccess({
      //       data: result.data.data.membershipSettings,
      //       partialChanged: true,
      //     })
      // )

      toast.success('Changes saved successfully')
      setOriginalAboutText(aboutText)
      setHasTitleChanges(false)
      setSaving(false)
      closeEditMode()
    } catch (error) {
      if (error) {
        console.error(error)
      }
      toast.error(formSubmitError)
      if (hasAboutTextChanges) {
        setEditingAboutText(true)
      } else if (!hasTitleChanges && !hasGalleryChanges) {
        closeEditMode()
      }
    } finally {
      setSavingCommunityData(false)
      setSaving(false)
    }
  }

  const discardChanges = () => {
    if (hasGalleryChanges) {
      setGalleryMedia(initialGalleryMediaRef.current)
    }

    if (hasAboutTextChanges) {
      setAboutText(originalAboutText)
      setEditingAboutText(false)
    }

    closeEditMode()
  }

  const handleDeleteMedia = (id: string) => {
    const updatedGalleryMedia = galleryMedia.filter((item) => item.id !== id)
    setGalleryMedia(
      updatedGalleryMedia.length === 6 && updatedGalleryMedia[updatedGalleryMedia.length - 1]?.id !== 'uploader'
        ? [...updatedGalleryMedia, { id: 'uploader' }]
        : updatedGalleryMedia,
    )
  }

  const startUploadPolling = async (uploadId: any) => {
    if (!(muxUploadPolling.current[uploadId] === undefined || muxUploadPolling.current[uploadId] === null)) return

    muxUploadPolling.current[uploadId] = {
      interval: null,
      status: 'queued',
    }

    const pollMuxUpload = async () => {
      if (['in-progress', 'done'].includes(muxUploadPolling.current[uploadId]?.status)) return

      muxUploadPolling.current[uploadId].status = 'in-progress'

      const res = await getMuxUploadApi(uploadId)
      if (!mountedRef.current) return

      const assetId = res?.data?.data?.asset_id
      if (!assetId) muxUploadPolling.current[uploadId].status = 'queued'

      const {
        data: { data: assetData },
      } = await getMuxAssetApi(assetId)
      if (!mountedRef.current) return

      if (assetData?.playback_ids?.[0]?.id) {
        muxUploadPolling.current[uploadId].status = 'done'

        clearInterval(muxUploadPolling.current[uploadId].interval)
        setTempGalleryVideoData((tempData) => ({
          ...tempData,
          [uploadId]: assetData,
        }))
      }
    }

    muxUploadPolling.current[uploadId].interval = setInterval(async () => {
      pollMuxUpload()
    }, 5000)

    pollMuxUpload()
  }

  useEffect(() => {
    galleryMedia?.forEach((item) => {
      if (item?.mux_upload_id && !item.mux_asset?.playback_ids?.[0]?.id) {
        startUploadPolling(item.mux_upload_id)
      }
    })
  }, [galleryMedia])

  const getAssetData = (item: any) => {
    if (!item?.mux_upload_id) return

    if (item?.mux_asset?.playback_ids?.[0]?.id) {
      return item.mux_asset
    }
    if (tempGalleryVideoData[item.mux_upload_id]) {
      return tempGalleryVideoData[item?.mux_upload_id]
    }
  }

  const cancelUploads = async () => {
    cancel()
    setSaving(false)
    if (hasAboutTextChanges) {
      setEditingAboutText(true)
    } else {
      closeEditMode()
    }
  }

  return (
    <>
      <div className="relative min-h-dvh w-full rounded-none bg-white-500 dark:bg-black-500 md:h-full md:min-h-full md:rounded-base">
        <div className="flex w-full flex-col gap-2 px-5 pt-5">
          <Input
            autoComplete="off"
            className="w-full"
            inputClassName="text-xl font-semibold"
            disabled={false}
            type="text"
            placeholder="Title"
            value={aboutTitle}
            onChange={(e) => {
              setAboutTitle(e.target.value)
              setHasTitleChanges(true)
            }}
            maxLength={titleMaxLength}
          />
          <div className="text-right text-xs text-black-200 dark:text-black-100">
            {aboutTitle.length} / {titleMaxLength} characters
          </div>
        </div>

        <div className="p-5">
          <EditableGallery
            getAssetData={getAssetData}
            fileUploads={fileUploads}
            handleDelete={handleDeleteMedia}
            media={galleryMedia}
            saving={saving}
            setMedia={setGalleryMedia}
          />
        </div>
        <div className="inline-flex w-full flex-wrap items-center justify-start gap-4 px-5 py-[0.625rem] text-xs font-semibold text-black-700 dark:border-grey-900 dark:text-white-500 md:flex-nowrap md:gap-10">
          <div className="flex items-center gap-1">
            {isPublicCommunity(membershipSetting) ? (
              <PublicCommunity12Icon className="mr-2 inline h-5 w-5 shrink-0 align-middle" />
            ) : (
              <Lock20Icon className="mr-2 inline h-5 w-5 shrink-0 align-middle" />
            )}
            {isPublicCommunity(membershipSetting) ? 'Public' : 'Private'}
          </div>
          <div className="flex items-center gap-1">
            <div className="relative h-5 w-[1.375rem] overflow-hidden">
              <Group24Icon className="absolute -top-0.5 h-[1.375rem] w-[1.375rem]" />
            </div>
            {membersCount && (
              <div>
                {formatThousands(membersCount)}
                <span className="hidden lg:inline"> {membersCount !== 1 ? 'Members' : 'Member'}</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-1">
            <div className="h-5 w-5">
              <Tag20Icon />
            </div>
            <div>{getCommunityPricing(membershipSetting) ? 'Paid' : 'Free'}</div>
          </div>
          {membership?.owner && (
            <div className="flex items-center gap-1">
              <div>
                <ProfilePicture
                  className="h-6 w-6"
                  src={membership.owner.profile.image}
                  cropArea={membership.owner.profile.image_crop_area}
                  alt={getMemberFullName(membership.owner)}
                  height={24}
                  width={24}
                />
              </div>
              <div>By {getMemberFullName(membership.owner)}</div>
            </div>
          )}
        </div>
        <TextEditor
          characterLimit={2000}
          className="p-5 pt-[1.9375rem] text-base text-black-600 dark:text-white-200 sm:text-sm"
          editing={true}
          label="Description"
          placeholder="Add a description..."
          setEditing={setEditingAboutText}
          value={aboutText}
          setValue={setAboutText}
          autoFocus={false}
        />
        <div className="flex justify-end space-x-3 pb-5 pl-5 pr-5">
          {!savingCommunityData && (
            <Button
              variant="outline"
              disabled={savingCommunityData}
              onClick={() => {
                if (saving) {
                  cancelUploads()
                } else if (hasChanges) {
                  setDiscardChangesWarningOpen(true)
                } else {
                  closeEditMode()
                }
              }}
            >
              {saving ? 'Cancel save' : 'Cancel'}
            </Button>
          )}
          <Button disabled={saving} loading={saving} onClick={handleSave} variant="community-primary">
            Save
          </Button>
        </div>
      </div>

      <ConfirmModal
        title="Are you sure you want to discard changes?"
        onConfirm={() => {
          setDiscardChangesWarningOpen(false)
          setEditingAboutText(false)
          discardChanges()
        }}
        open={discardChangesWarningOpen}
        onCancel={() => setDiscardChangesWarningOpen(false)}
      />
    </>
  )
}
