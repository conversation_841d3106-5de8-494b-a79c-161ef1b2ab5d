import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import React from 'react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { Button } from '@/components/ui'
import { getFullName } from '@/shared-libs/profile'

export default function MemberRequestCardHeader({ memberRequest, onApproveClick, onDeclineClick }) {
  console.log('member request', memberRequest)
  const fullName = getFullName(memberRequest.user.first_name, memberRequest.user.last_name)

  return (
    <div className="flex flex-wrap items-center">
      <AppProfileImage
        imageUrl={memberRequest.user.profile.image}
        cropArea={memberRequest.user.profile.image_crop_area}
        name={fullName}
        size={40}
      />
      <div className="flex flex-grow flex-col p-3">
        <div className="text-white font-['Graphik'] text-base font-semibold leading-normal">{fullName}</div>
        <div>
          <span className="font-['Graphik'] text-[13px] font-normal leading-[14px] text-[#8d94a3]">Requested </span>
          <span className="font-['Graphik'] text-[13px] font-semibold text-[#8d94a3]">
            {getDateTimeFromNow(memberRequest.created_at)}
          </span>
        </div>
      </div>
      <div className="mx-auto mb-3 flex gap-3 sm:mx-0 sm:mb-0">
        <Button
          type="submit"
          variant="outline"
          onClick={() => onApproveClick(memberRequest)}
          data-cy="approval-all-button"
        >
          <CheckIcon />
          Approve
        </Button>

        <Button
          type="submit"
          variant="outline"
          onClick={() => onDeclineClick(memberRequest)}
          data-cy="approval-all-button"
        >
          <CloseIcon />
          Decline
        </Button>
      </div>
    </div>
  )
}
