import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { AspectRatio } from '@radix-ui/react-aspect-ratio'
import Image from 'next/image'

import { Plus16Icon } from '@/components/icons'
import { HourglassProgress24Icon } from '@/components/icons/24px/hourglass-progress-24-icon'
import { CroppedImage } from '@/components/images/cropped-image'
import { cn } from '@/lib/utils'

export function StaticGalleryThumbnail({
  active,
  className,
  media,
  playbackId,
  onClick,
}: {
  active?: boolean
  className?: string
  media?: any
  playbackId?: string
  onClick?: () => void
}) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: media.id,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      style={style}
      ref={setNodeRef}
      className={cn(
        'group relative w-[calc((100%-3rem)/7)] md:w-[calc((100%-3.75rem)/7)] lg:w-[calc((100%-6rem)/7)]',
        className,
      )}
      onClick={onClick}
      {...attributes}
      {...listeners}
    >
      <AspectRatio ratio={1}>
        {media.id === 'uploader' ? (
          <div
            className={cn(
              'flex h-full w-full cursor-pointer items-center justify-center overflow-hidden rounded-base bg-white-100 transition-opacity dark:bg-black-300',
              !active && 'opacity-50 hover:opacity-70',
            )}
          >
            <Plus16Icon />
          </div>
        ) : (
          <div
            className={cn(
              'h-full w-full overflow-hidden rounded-base bg-white-100 transition-opacity dark:bg-black-300',
              !active && 'opacity-50 hover:opacity-70',
            )}
          >
            {playbackId ? (
              <Image
                className="h-full w-full rounded-base bg-white-100 object-cover dark:bg-black-300"
                src={`https://image.mux.com/${playbackId}/thumbnail.jpg?width=200&height=200&fit_mode=crop`}
                alt=""
                width={100}
                height={100}
                unoptimized
              />
            ) : (
              <>
                {media?.url && (
                  <CroppedImage
                    className="h-full w-full"
                    src={media.url}
                    cropArea={media.cropArea}
                    width={100}
                    height={100}
                    alt=""
                    priority
                  />
                )}
                {media?.mux_upload_id && media?.mux_asset?.status !== 'ready' && (
                  <div
                    className={cn(
                      'absolute left-0 top-0 flex h-full w-full items-center justify-center',
                      media?.url && 'bg-black-500/50',
                    )}
                  >
                    <HourglassProgress24Icon />
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </AspectRatio>
    </div>
  )
}
