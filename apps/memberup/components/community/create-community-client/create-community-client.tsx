'use client'

import { useRouter } from 'next/navigation'

import { CommunityCreationFormContainer } from '@/components/community/community-creation-form'
import { ParticlesContainer } from '@/components/layout/particles-container'
import { PrimaryBlurredBackground } from '@/components/layout/primary-blurred-background'
import { useRouteProtection } from '@/hooks/useRouteProtection'
import { useStore } from '@/hooks/useStore'
import { getActiveUserApi } from '@/shared-services/apis/user.api'
import { IMembership } from '@/shared-types/interfaces'

export function CreateCommunityClient() {
  useRouteProtection()
  const router = useRouter()
  const setAuthenticatedUserData = useStore((state) => state.auth.setAuthenticatedUserData)
  const setNewlyCreatedId = useStore((state) => state.community.setNewlyCreatedId)
  const setMembership = useStore((state) => state.community.setMembership)

  const onSuccess = async (newCommunity: IMembership) => {
    // Update User's user_memberships data
    const {
      data: { data },
    } = await getActiveUserApi()
    setAuthenticatedUserData(data)
    setNewlyCreatedId(newCommunity.id)
    setMembership(newCommunity)
    router.push(`/${newCommunity.slug}`)
  }

  return (
    <>
      <PrimaryBlurredBackground className="-top-20" />
      <ParticlesContainer className="relative z-20 mx-auto w-full rounded-base border border-grey-900 px-4 py-8 sm:px-8">
        <CommunityCreationFormContainer onSuccess={onSuccess} />
      </ParticlesContainer>
      <PrimaryBlurredBackground className="-bottom-[19rem]" />
    </>
  )
}
