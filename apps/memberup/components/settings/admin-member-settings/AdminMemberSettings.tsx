import { useState } from 'react'

import { AdminMembershipSettings } from './AdminMembershipSettings'
import { Favicon } from '@/components/community/favicon'
import { CoursesSettings } from '@/components/settings/admin-member-settings/CoursesSettings'
import { QuestionsSettings } from '@/components/settings/admin-member-settings/QuestionsSettings'
import { SettingsModal, SettingsModalConfig } from '@/components/ui/settings-modal'
import { useStore } from '@/hooks/useStore'
import { IUser } from '@/shared-types/interfaces'

/*
This opens from the community info as a member of the community.
 */
export function AdminMemberSettings({
  open,
  onOpenChange,
  defaultSection,
  member,
}: {
  open: boolean
  onOpenChange: (value: boolean) => void
  defaultSection?: string
  member: IUser
}) {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting

  if (!membership) {
    return
  }

  const config: SettingsModalConfig = {
    sections: [
      {
        name: 'membership',
        title: 'Membership',
        component: () => <AdminMembershipSettings member={member} />,
      },
      {
        name: 'courses',
        title: 'Courses',
        component: () => <CoursesSettings member={member} />,
      },
      {
        name: 'questions',
        title: 'Questions',
        component: () => <QuestionsSettings member={member} />,
      },
    ],
  }

  return (
    <SettingsModal
      header={
        <>
          <Favicon
            className="mr-3 h-12 w-12"
            communityName={membership.name}
            cropArea={membershipSetting.favicon_crop_area}
            src={membershipSetting.favicon}
            width={48}
            height={48}
          />
          <div className="select-none">
            <h1 className="text-base font-semibold text-black-700 dark:text-white-500">
              {member?.first_name} {member?.last_name}
            </h1>
            <p className="text-ssm text-black-200 dark:text-black-100">Member settings</p>
          </div>
        </>
      }
      open={open}
      onOpenChange={onOpenChange}
      config={config}
      defaultSection={defaultSection}
      openFromUrl={false}
    />
  )
}
