import { useRouter } from 'next/navigation'
import { useState } from 'react'
import <PERSON><PERSON> from 'stripe'

import { MembershipSettingsSections } from './constants'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { formatCommunitySubscriptionAmountSuffix } from '@/lib/stripe'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { cancelMembershipApi } from '@/shared-services/apis/user.api'

interface CancelMembershipProps {
  subscription: Stripe.Subscription | null
  nextInvoice: Stripe.Invoice | null
  setSection: (section: MembershipSettingsSections) => void
}

export function CancelMembership({ subscription, nextInvoice, setSection }: CancelMembershipProps) {
  const membership = useStore((state) => state.community.membership)
  const [cancellingMembership, setCancellingMembership] = useState(false)
  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)
  const router = useRouter()

  const cancelMembership = async () => {
    setCancellingMembership(true)
    try {
      const res = await cancelMembershipApi(membership.id)
      if (res.data.success) {
        toast.success('You have left the community')
        updateUserMembership(res.data.data)
        router.push(getCommunityBaseURL(membership) + '/about')
      }
    } catch {
      toast.error(formSubmitError)
      setCancellingMembership(false)
    }
  }

  const cancelActionTitle = subscription ? 'Cancel membership' : 'Leave community'

  return (
    <div className="pt-3 text-sm sm:pt-0">
      <h2 className="mb-3 text-base font-semibold text-black-600 dark:text-white-200">{cancelActionTitle}</h2>
      {subscription ? (
        <>
          <p className="mb-2">Are you sure you want to cancel your {membership.name} membership?</p>
          <ul className="mb-3 list-disc pl-5">
            <li>You will be removed from the community</li>
            <li>You will loose access to all courses, events, and members only community</li>
          </ul>
          <p className="mb-2 text-base font-semibold text-red-200">Warning</p>
          {nextInvoice && (
            <p>
              You&apos;re paying ${nextInvoice ? (nextInvoice.total / 100).toFixed(2) : '0'}
              {subscription ? formatCommunitySubscriptionAmountSuffix(subscription) : '/month'}. The price may increase
              later if you want to join again.
            </p>
          )}
        </>
      ) : (
        <>
          <p className="mb-6 text-black-600 dark:text-black-100 sm:mb-5">
            Are you sure you want to leave <b>{membership.name}</b>? If you rejoin, you&apos;ll have to request
            membership and be approved by an admin again.
          </p>
        </>
      )}
      {subscription ? (
        <div className="flex space-x-5 pt-6">
          <Button
            variant="outline"
            onClick={() => {
              setSection(MembershipSettingsSections.MAIN)
            }}
          >
            Keep membership
          </Button>
          <Button
            disabled={cancellingMembership}
            className="text-black-200 transition-colors hover:text-red-200 dark:text-black-100 dark:hover:text-red-200"
            variant="inline"
            onClick={cancelMembership}
            loading={cancellingMembership}
          >
            Finish canceling
          </Button>
        </div>
      ) : (
        <div className="flex h-full flex-col-reverse gap-2 sm:h-10 sm:flex-row">
          <Button
            variant="outline"
            onClick={() => {
              setSection(MembershipSettingsSections.MAIN)
            }}
            className="h-full w-full px-7 py-3 sm:w-fit"
          >
            Stay
          </Button>
          <Button
            disabled={cancellingMembership}
            variant="destructive"
            onClick={cancelMembership}
            loading={cancellingMembership}
            className="h-full w-full px-7 py-3 sm:w-fit"
          >
            Leave
          </Button>
        </div>
      )}
    </div>
  )
}
