'use client'

import { Elements } from '@stripe/react-stripe-js'
import { loadStripe, PaymentMethod } from '@stripe/stripe-js'
import { useEffect, useMemo, useState } from 'react'

import { STRIPE_PUBLISH_KEY } from '@memberup/shared/src/config/envs'
import {
  attachAndSetDefaultStripePaymentMethodApi,
  createStripeMembershipSetupIntentApi,
} from '@memberup/shared/src/services/apis/stripe.api'
import { ErrorMessageRetry } from '@/components/layout/error-message-retry/error-message-retry'
import { PaymentMethodForm } from '@/components/payments/add-payment-method-dialog/payment-method-form'
import { SkeletonBox } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { warn } from '@/lib/error-handling'
import { getPaymentMethodErrorMessage, getSetupIntentErrorMessage } from '@/lib/error-messages'
import { STRIPE_PAYMENT_METHOD_ERROR_CODE_ENUM, STRIPE_SETUP_INTENT_ERROR_CODE_ENUM } from '@/shared-types/enum'

interface MembershipUpdatePaymentMethodProps {
  onClose: () => void
}

export function MembershipUpdatePaymentMethod({ onClose }: MembershipUpdatePaymentMethodProps) {
  const membership = useStore((state) => state.community.membership)
  const [clientSecret, setClientSecret] = useState<string | null>(null)
  const [stripeLoading, setStripeLoading] = useState(true)
  const [error, setError] = useState(false)

  const fetchSetupIntent = async () => {
    try {
      const connectAccount = membership.membership_setting?.stripe_connect_account

      if (!connectAccount?.stripe_user_id && !connectAccount?.stripe_publishable_key) {
        toast.error('No Stripe Connect account found for this community')
        return
      }

      setStripeLoading(true)
      setError(false)
      const response = await createStripeMembershipSetupIntentApi(membership.id, true)
      setClientSecret(response.data.data?.['client_secret'])
      setStripeLoading(false)
    } catch (error) {
      const errorCode = error.response?.data?.error?.code
      const errorMessage = getSetupIntentErrorMessage(errorCode)

      if (!errorCode || !Object.values(STRIPE_SETUP_INTENT_ERROR_CODE_ENUM).includes(errorCode)) {
        warn(`Unknown Stripe setup intent error: ${error}`)
      }

      toast.error(errorMessage)
      setStripeLoading(false)
      setError(true)
    }
  }

  useEffect(() => {
    if (membership?.id) {
      fetchSetupIntent()
    }
  }, [membership?.id])

  const handlePaymentMethodUpdate = async (paymentMethod: PaymentMethod) => {
    if (paymentMethod && membership?.id) {
      try {
        await attachAndSetDefaultStripePaymentMethodApi(paymentMethod.id, membership.id)
        onClose()
        toast.success('Payment method updated successfully')
      } catch (error) {
        const errorCode = error.response?.data?.error?.code
        const errorMessage = getPaymentMethodErrorMessage(errorCode)

        if (!errorCode || !Object.values(STRIPE_PAYMENT_METHOD_ERROR_CODE_ENUM).includes(errorCode)) {
          warn(`Unknown Stripe payment method update error: ${error}`)
        }

        toast.error(errorMessage)
      }
    }
  }

  const showCardElement = !stripeLoading && !error && !!clientSecret

  const stripePromiseWithAccount = useMemo(() => {
    const connectAccount = membership.membership_setting?.stripe_connect_account
    if (!connectAccount) return null

    if (connectAccount.stripe_publishable_key && connectAccount.access_token) {
      return loadStripe(connectAccount.stripe_publishable_key)
    }

    if (connectAccount.stripe_user_id) {
      return loadStripe(STRIPE_PUBLISH_KEY, {
        stripeAccount: connectAccount.stripe_user_id,
      })
    }

    return null
  }, [membership.membership_setting?.stripe_connect_account])

  return (
    <div className="h-full">
      <h2 className="mb-3 text-lg font-semibold text-black-700 dark:text-white-500">Update Payment Method</h2>

      {stripeLoading && <SkeletonBox />}

      {error && (
        <ErrorMessageRetry
          onRetry={fetchSetupIntent}
          errorDescription="An error occurred while setting up the payment method."
        />
      )}

      {showCardElement && (
        <div className="h-[calc(100vh-24rem)] md:h-auto">
          <Elements
            stripe={stripePromiseWithAccount}
            options={{
              clientSecret,
              appearance: {
                theme: 'night',
              },
              loader: 'always',
            }}
          >
            <PaymentMethodForm
              paymentMethods={[]}
              onSuccess={handlePaymentMethodUpdate}
              onCancel={onClose}
              updateDefault={true}
              clientSecret={clientSecret}
              fullHeight={true}
              isMembershipPaymentMethod={true}
            />
          </Elements>
        </div>
      )}
    </div>
  )
}
