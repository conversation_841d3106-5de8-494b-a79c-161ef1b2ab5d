import { zodResolver } from '@hookform/resolvers/zod'
import { usePostHog } from 'posthog-js/react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import { ImageDimensionsValidator } from 'use-file-picker/validators'
import { z } from 'zod'

import { Plus12Icon } from '@/components/icons'
import { ImageCropper } from '@/components/images/image-cropper'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button, Input } from '@/components/ui'
import { Form, FormControl, FormCounter, FormField, FormItem } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useUpdateUserProfile } from '@/hooks/users/useUpdateUserProfile'
import { useStore } from '@/hooks/useStore'
import { getCroppedImg } from '@/lib/client/images'
import { maxBioLength, profilePictureMinHeight, profilePictureMinWidth } from '@/lib/constants'
import { formSubmitError, getMinImageDimensionsError } from '@/lib/error-messages'
import { UserEvents } from '@/lib/posthog'
import { getFullName } from '@/shared-libs/profile'
import { IUser } from '@/shared-types/interfaces'
import { CropArea } from '@/shared-types/types'

const formSchema = z.object({
  bio: z.string().max(maxBioLength),
})

type ProfileSetupFormSchemaType = z.infer<typeof formSchema>

export function ProfileSetupForm() {
  const posthog = usePostHog()
  const user = useStore((state) => state.auth.user)
  const profile = useStore((state) => state.auth.profile)
  const [profilePictureData, setProfilePictureData] = useState(null)
  const [cropArea, setCropArea] = useState(null)
  const minWidth = profilePictureMinWidth
  const minHeight = profilePictureMinHeight
  const { saving, updateUserProfile } = useUpdateUserProfile()

  const form = useForm<ProfileSetupFormSchemaType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      bio: '',
    },
    resolver: zodResolver(formSchema),
  })

  const updatedUserData = {
    ...user,
    profile: { ...profile },
  } as IUser

  if (profilePictureData) {
    Object.assign(updatedUserData.profile, profilePictureData)
  }

  const { clear, errors, openFilePicker, plainFiles } = useFilePicker({
    accept: 'image/*',
    multiple: false,
    readAs: 'DataURL',
    validators: [
      new ImageDimensionsValidator({
        minWidth,
        minHeight,
      }),
    ],
  })

  const fullName = getFullName(user.first_name, user.last_name)

  const imageCropperSettings =
    plainFiles?.[0]?.type?.indexOf('image') >= 0
      ? {
          url: URL.createObjectURL(plainFiles[0]),
          file: plainFiles[0],
        }
      : null

  const onCropComplete = async () => {
    const croppedImageBlob = await getCroppedImg(imageCropperSettings.url, cropArea)
    setProfilePictureData({
      image: croppedImageBlob,
      image_file: imageCropperSettings.file,
      image_crop_area: cropArea,
    })
    clear()
  }

  const onSubmit = async (values: ProfileSetupFormSchemaType) => {
    const submitData = { ...values }
    Object.assign(submitData, profilePictureData)

    const success = await updateUserProfile(submitData)

    if (success) {
      posthog.capture(UserEvents.ONBOARDING_BIO_COMPLETED, {
        bio_completed: Boolean(values.bio),
      })
    } else {
      toast.error(formSubmitError)
    }
  }

  if (imageCropperSettings) {
    return (
      <div className="flex flex-col items-center">
        <h2 className="mb-6 text-center text-lg font-semibold text-black-700 dark:text-white-500">Adjust your image</h2>
        <ImageCropper
          aspectRatio={1}
          url={imageCropperSettings.url}
          minWidth={minWidth}
          minHeight={minHeight}
          file={imageCropperSettings.file}
          open={true}
          setCropArea={setCropArea}
          onClose={() => clear()}
          cropShape="round"
        />
        <Button className="w-full" onClick={onCropComplete}>
          Save
        </Button>
      </div>
    )
  }

  return (
    <Form {...form}>
      <h2 className="mb-6 text-center text-lg font-semibold text-black-700 dark:text-white-500">
        Upload Your Profile Picture
      </h2>
      <div className="mb-4 flex flex-col items-center">
        <div className="relative z-0 h-32 w-32 shrink-0 overflow-hidden rounded-full">
          <ProfilePicture
            src={profilePictureData?.image}
            cropArea={cropArea}
            width={64}
            height={64}
            alt={fullName}
            displayInitials={false}
          />
        </div>
        <Button
          className="bg-white-700 relative z-10 -mt-3 dark:bg-black-500 dark:hover:bg-[#22252b]"
          variant="outline"
          size="xs"
          onClick={() => openFilePicker()}
        >
          <Plus12Icon className="mr-1" />
          {profilePictureData ? 'Change photo' : 'Upload photo'}
        </Button>
        {errors && errors.length > 0 && (
          <p className="pt-5 text-sm text-red-200">{getMinImageDimensionsError(minWidth, minHeight)}</p>
        )}
      </div>
      <p className="mb-4 text-center text-ssm text-black-200 dark:text-black-100">
        Communities are better with profile pictures.
        <br />
        Upload yours to enter the community.
      </p>
      <FormField
        control={form.control}
        name="bio"
        render={({ field, fieldState: { error } }) => (
          <FormItem>
            <FormControl>
              <Input
                className="w-full"
                disabled={saving}
                placeholder="Bio"
                error={Boolean(error)}
                maxLength={maxBioLength}
                {...field}
              />
            </FormControl>
            <FormCounter>
              {form.getValues('bio').length}/{maxBioLength}
            </FormCounter>
          </FormItem>
        )}
      />
      <Button
        className="mt-4 w-full"
        disabled={!profilePictureData || saving}
        loading={saving}
        onClick={form.handleSubmit(onSubmit)}
      >
        Next
      </Button>
    </Form>
  )
}
