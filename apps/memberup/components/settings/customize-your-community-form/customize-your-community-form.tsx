'use client'

import Image from 'next/image'
import { useEffect, useState } from 'react'

import { MemberupM24Icon } from '@/components/icons/24px/memberup-m-24-icon'
import { Button } from '@/components/ui'
import { ColorPickerPopover } from '@/components/ui/color-picker/color-picker-popover'
import { Ripple } from '@/components/ui/ripple'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { colorPickerContrastReferenceColors, defaultCommunityColors } from '@/lib/constants'
import { formSubmitError } from '@/lib/error-messages'
import { cn } from '@/lib/utils'
import { updateMembershipSettingSuccess } from '@/memberup/store/features/membershipSlice'
import gradientCircle from '@/public/assets/images/gradient-circle-round.png'
import { updateMembershipApi } from '@/shared-services/apis/membership.api'
import { useAppDispatch } from '@/src/store/hooks'

export function CustomizeYourCommunityForm() {
  const dispatch = useAppDispatch()
  const communityColors = useStore((state) => state.community.colors)
  const setOverrideThemeMainColor = useStore((state) => state.community.setOverrideThemeMainColor)
  const membership = useStore((state) => state.community.membership)
  const setMembership = useStore((state) => state.community.setMembership)
  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)
  const [selectedColor, setSelectedColor] = useState(communityColors.primary)
  const [saving, setSaving] = useState(false)
  const [colorPickerOpen, setColorPickerOpen] = useState(false)
  const [colorPickerUsed, setColorPickerUsed] = useState(false)

  const getIsSimpleColorSelected = (color: string) => {
    return selectedColor === color && !colorPickerOpen
  }

  useEffect(() => {
    return () => {
      setOverrideThemeMainColor(null)
    }
  }, [setOverrideThemeMainColor])

  // Reset color picker selection when a simple color is selected and picker is closed
  useEffect(() => {
    if (!defaultCommunityColors.includes(selectedColor) || colorPickerOpen) {
      return
    }
    setColorPickerUsed(false)
  }, [selectedColor, colorPickerOpen])

  const handleSave = async () => {
    setSaving(true)
    const data = {
      theme_main_color: selectedColor,
    }

    const result = await updateMembershipApi(data as any, membership.id)

    if (!result.data.success) {
      toast.error(formSubmitError)
    }

    const updatedMembership = result.data.data.membership
    const updatedMembershipSettings = result.data.data.membership.membership_settings
    const updatedUserMembership = result.data.data.user_membership

    // As the membership is also in the user membership we need to reload both.
    setMembership({
      ...updatedMembership,
    })
    updateUserMembership(updatedUserMembership)

    dispatch(
      updateMembershipSettingSuccess({
        data: updatedMembershipSettings,
        partialChanged: true,
      }),
    )
    setSaving(false)
  }

  const activateColor = (color: string) => {
    setOverrideThemeMainColor(color)
  }

  const deactivateColor = () => {
    setOverrideThemeMainColor(selectedColor)
  }

  const handleOnColorPickerClick = () => {
    setColorPickerUsed(true)
    setColorPickerOpen(true)
  }

  const handleOnColorPickerColorChange = (color: string) => {
    setSelectedColor(color)
    setOverrideThemeMainColor(color)
  }

  const handleOnSimpleColorClick = (color: string) => {
    setSelectedColor(color)
    setOverrideThemeMainColor(color)
    setColorPickerUsed(false)
  }

  return (
    <div>
      <div className="flex justify-center">
        <div className="relative flex h-[257px] w-full transform items-center justify-center">
          <Ripple circleScaleFactor={44} mainCircleSize={54} numCircles={5} />
          <MemberupM24Icon
            className="h-[1.75rem] w-[1.75rem]"
            style={{ color: `color-mix(in srgb, ${communityColors.primary}, black 40%)` }}
          />
        </div>
      </div>
      <div className="mt-6 flex items-center justify-between rounded-base bg-black-100/10 p-2.5 text-xs font-medium">
        <span className="mr-6">Primary</span>
        {defaultCommunityColors.map((c) => {
          return (
            <span
              key={c}
              className={cn(
                'flex h-7 w-7 items-center justify-center rounded-full border-2 p-0.5 transition-colors hover:border-black-700 hover:dark:border-white-500',
                getIsSimpleColorSelected(c)
                  ? 'border-black-700 dark:border-white-500'
                  : 'cursor-pointer border-transparent',
              )}
              onClick={() => handleOnSimpleColorClick(c)}
              onMouseEnter={() => activateColor(c)}
              onMouseLeave={() => deactivateColor()}
            >
              <span className="inline-block h-5 w-5 shrink-0 rounded-full" style={{ backgroundColor: c }} />
            </span>
          )
        })}
        <span
          className={cn(
            'flex h-7 w-7 items-center justify-center rounded-full border-2 p-0.5 transition-colors',
            colorPickerUsed
              ? 'border-black-700 dark:border-white-500'
              : 'cursor-pointer border-transparent hover:border-black-700 hover:dark:border-white-500',
          )}
        >
          <ColorPickerPopover
            open={colorPickerOpen}
            onChange={(color: string) => handleOnColorPickerColorChange(color)}
            setOpen={setColorPickerOpen}
            value={selectedColor}
            referenceColors={colorPickerContrastReferenceColors}
          >
            <Image
              src={gradientCircle}
              width={20}
              height={20}
              priority
              alt="Color Picker"
              onClick={handleOnColorPickerClick}
            />
          </ColorPickerPopover>
        </span>
      </div>
      <Button
        className="mt-6 w-full"
        type="submit"
        variant="community-primary"
        loading={saving}
        onClick={handleSave}
        disabled={saving}
      >
        Done
      </Button>
    </div>
  )
}
