import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button, Input } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { useAppSelector } from '@/memberup/store/hooks'
import { inviteApi } from '@/shared-services/apis/invite.api'
import { selectMembership } from '@/src/store/features/membershipSlice'

function InviteUserForm({
  onSuccess,
  requestingInvite,
  setRequestingInvite,
}: {
  onSuccess: (newEmail: string, newPassword: string) => void
  requestingInvite: boolean
  setRequestingInvite: (value: boolean) => void
}) {
  const [errorMessage, setErrorMessage] = useState(null)
  const membership = useStore((state) => state.community.membership)

  const changeEmailSchema = z.object({
    email: z.string().email(),
  })

  type InviteUserSchemaType = z.infer<typeof changeEmailSchema>
  const inviteUserForm = useForm<InviteUserSchemaType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
    },
    resolver: zodResolver(changeEmailSchema),
  })

  const onInviteUserSubmit = async (formData: InviteUserSchemaType) => {
    setRequestingInvite(true)
    try {
      const res = await inviteApi({ members: [{ email: formData.email }], message: null }, membership.id)
      if (res.data.success) {
        inviteUserForm.reset(
          {
            email: '',
          },
          { keepIsSubmitted: false },
        )
        toast.success('Your invite has been sent.', 'success')
      }
    } catch (err) {
      const errorMessage = err?.response?.data?.message || err?.message
      if (errorMessage) {
        toast.error(errorMessage, 'error')
      }
    } finally {
      setRequestingInvite(false)
    }
  }

  // @ts-ignore
  // @ts-ignore
  return (
    <>
      <Form {...inviteUserForm}>
        <div className="flex flex-row gap-5">
          <div className={'w-full'}>
            <FormField
              className={'blah'}
              control={inviteUserForm.control}
              name="email"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      className="w-full"
                      disabled={requestingInvite}
                      name="email"
                      type="email"
                      placeholder="Email"
                      error={Boolean(error)}
                      variant="dialog"
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
          </div>
          <Button
            className="w-full sm:w-[5.625rem]"
            variant="default"
            type="submit"
            loading={requestingInvite}
            disabled={requestingInvite}
            onClick={inviteUserForm.handleSubmit(onInviteUserSubmit)}
          >
            Send
          </Button>
        </div>

        {errorMessage && <FormMessage className="mt-5">{errorMessage}</FormMessage>}
      </Form>
    </>
  )
}

export { InviteUserForm }
