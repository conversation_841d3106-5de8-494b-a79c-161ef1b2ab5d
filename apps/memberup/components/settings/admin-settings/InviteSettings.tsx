import { useState } from 'react'

import { InviteUserForm } from '@/components/settings/admin-settings/invite-user-form'
import BulkInviteForm from '@/components/settings/admin-settings/invite/BulkInviteForm'
import { ClipboardCopyInput } from '@/components/ui/clipboard-copy-input'
import { useStore } from '@/hooks/useStore'

export function InviteSettings() {
  const membership = useStore((state) => state.community.membership)
  const [requestingInvite, setRequestingInvite] = useState(false)
  const [modeBulkImport, setModeBulkImport] = useState(false)
  const [bulkImportSuccess, setBulkImportSuccess] = useState(false)

  const handleImportButtonClick = () => {
    setModeBulkImport(true)
  }
  const handleBulkImportBackClick = () => {
    setModeBulkImport(false)
  }

  const communityInviteLink = `https://${process.env.NEXT_PUBLIC_DEFAULT_DOMAIN}/${membership.slug}/about`

  return (
    <div className={'space-y-6'}>
      {!modeBulkImport && (
        <>
          <h2 className="text-lg font-semibold text-white-500">Share your invite link</h2>
          <div className="text-sm font-normal leading-snug text-black-600 dark:text-white-200">
            This will take people to your about page where they can purchase or request membership.
          </div>
          <ClipboardCopyInput value={communityInviteLink} />
          <hr />
          <div className="text-sm font-normal leading-snug text-black-600 dark:text-white-200">
            These invite methods provide instant access without purchase or membership requests.
          </div>

          <InviteUserForm
            onSuccess={() => {}}
            requestingInvite={requestingInvite}
            setRequestingInvite={setRequestingInvite}
          />
          {/*<div className={'flex flex-row'}>
            <div className={''}>[ICON]</div>
            <div className={'flex'}>
              <div className="text-white text-sm font-medium font-['Graphik']">
                Import .CSV file
              </div>
              <div className="text-[#8d94a3] text-[13px] font-normal font-['Graphik']">
                Invite members in bulk by uploading a .CSV file of addresses
              </div>
              <Button
                color="primary"
                variant="outline"
                size={'sm'}
                onClick={handleImportButtonClick}
              >
                Import
              </Button>
            </div>
          </div>*/}
        </>
      )}
      {modeBulkImport && (
        <>
          <div className="text-white font-['Graphik'] text-base font-semibold leading-normal">Invite .CSV file</div>
          <span
            className="cursor-pointer text-sm font-normal leading-snug text-primary-100"
            onClick={handleBulkImportBackClick}
          >
            Back to invite
          </span>
          {!bulkImportSuccess && <BulkInviteForm onSuccess={() => setBulkImportSuccess(true)} />}
          {bulkImportSuccess && <p>Bulk Import success</p>}
        </>
      )}
    </div>
  )
}
