'use client'

import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import { usePostHog } from 'posthog-js/react'
import React, { useEffect, useState } from 'react'
import { useChatContext } from 'stream-chat-react'

import { updateFeedApi } from '@memberup/shared/src/services/apis/feed.api'
import { FEED_STATUS_ENUM, FEED_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { PostSummary } from '@/components/feed/post-summary'
import { Button, SkeletonBox } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { FeedEvents } from '@/lib/posthog'

export function ModerationSettings() {
  const [isLoading, setIsLoading] = useState(true)
  const [isRequesting, setIsRequesting] = useState(false)
  const membership = useStore((state) => state.community.membership)
  const [reportedPosts, setReportedPosts] = useState([])
  const { client: streamChatClient } = useChatContext()
  const [messages, setMessages] = useState({
    previous: undefined,
    next: undefined,
    results: [],
  })
  const [action, setAction] = useState(null)
  const [selectedPost, setSelectedPost] = useState(null)
  const [openConfirm, setOpenConfirm] = useState(false)
  const [updatingPost, setUpdatingPost] = useState(false)
  const posthog = usePostHog()

  useEffect(() => {
    const initialize = async () => {
      const channelFilters = { team: membership.id }
      const messageFilters = {
        feed_type: { $in: [FEED_TYPE_ENUM.default] },
        feed_status: { $in: [FEED_STATUS_ENUM.reported] },
      }
      try {
        const res = await streamChatClient.search(channelFilters, messageFilters, { sort: { updated_at: 1 } })
        const temp = res.results.map((item) => item.message)
        setReportedPosts(res.results.map((r) => r.message))
        setMessages({
          previous: res.previous,
          next: res.next,
          results: messages.results.concat(temp),
        })
      } catch (e) {
        console.error(e)
      } finally {
        setIsLoading(false)
      }
    }
    initialize()
  }, [streamChatClient])

  const handleConfirmPostUpdate = async () => {
    setUpdatingPost(true)
    const status = action === 'approve' ? FEED_STATUS_ENUM.approved : FEED_STATUS_ENUM.rejected
    await handlePostUpdate(selectedPost.id, status)
    posthog.capture(FeedEvents.POST_MODERATED, {
      post_id: selectedPost.id,
      status,
    })
    setUpdatingPost(false)
    setOpenConfirm(false)
  }

  const handleConfirmModalClose = () => {
    setAction(null)
    setSelectedPost(null)
    setOpenConfirm(false)
  }

  const handleApproveButtonClick = (post) => {
    setSelectedPost(post)
    setAction('approve')
    setOpenConfirm(true)
  }

  const handleRejectButtonClick = (post) => {
    setSelectedPost(post)
    setAction('reject')
    setOpenConfirm(true)
  }

  const handlePostUpdate = async (messageId: string, status: FEED_STATUS_ENUM) => {
    // Optimistically remove the post from the reported posts.
    setReportedPosts(reportedPosts.filter((p) => p.id !== messageId))
    setIsRequesting(true)
    const data = {
      feed_status: status,
    }
    try {
      await updateFeedApi(messageId, data)
      toast.success(`The post has been ${status === FEED_STATUS_ENUM.approved ? 'approved' : 'rejected'}`)
    } catch (e) {
      console.error(e)
      toast.error('An unexpected error happened. Please try again')
    } finally {
      setIsRequesting(false)
    }
  }

  return (
    <div className={'space-y-6'}>
      <ConfirmModal
        title={`Are you sure you want to ${action} the post?`}
        onConfirm={handleConfirmPostUpdate}
        open={openConfirm}
        onCancel={handleConfirmModalClose}
        loading={updatingPost}
      />
      <h2 className="text-lg font-semibold text-white-500">Moderation</h2>
      <div className="font-['Graphik'] text-sm font-normal leading-snug text-[#8d94a3]">
        Approve and reject reported posts.
      </div>
      {isLoading && <SkeletonBox />}
      {reportedPosts.map((m) => {
        return (
          <div key={m.id}>
            <PostSummary
              readOnly={true}
              disableDetails={true}
              feed={m}
              membership={membership}
              extraHeaderComponents={
                <div className="flex gap-2 md:gap-3">
                  <Button
                    size="sm"
                    type="submit"
                    variant="outline"
                    loading={isRequesting && action === 'approve'}
                    disabled={isRequesting}
                    onClick={() => handleApproveButtonClick(m)}
                    data-cy="approval-all-button"
                    className="px-2 py-1 text-xs md:px-3 md:py-2 md:text-sm"
                  >
                    <CheckIcon className="h-6 w-6 md:h-4 md:w-4" />
                    <span className="hidden sm:inline">Approve</span>
                  </Button>
                  <Button
                    size="sm"
                    type="submit"
                    loading={isRequesting && action === 'reject'}
                    disabled={isRequesting}
                    variant="outline"
                    onClick={() => handleRejectButtonClick(m)}
                    data-cy="approval-all-button"
                    className="px-2 py-1 text-xs md:px-3 md:py-2 md:text-sm"
                  >
                    <CloseIcon className="h-6 w-6 md:h-4 md:w-4" />
                    <span className="hidden sm:inline">Reject</span>
                  </Button>
                </div>
              }
            />
          </div>
        )
      })}
      {!isLoading && reportedPosts.length === 0 && (
        <div className="inline-flex w-full items-center justify-between rounded-base bg-[#202124] px-5 py-10">
          <div className="inline-flex shrink grow basis-0 flex-col items-center justify-center gap-5">
            <div className="flex flex-col items-start justify-start gap-4">
              <div className="text-white self-stretch text-center font-['Graphik'] text-lg font-semibold leading-normal">
                You’re all clear! 🎉
              </div>
              <div className="w-[272px] text-center font-['Graphik'] text-xs font-normal leading-tight text-[#8d94a3]">
                There are no reported posts in your community
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
