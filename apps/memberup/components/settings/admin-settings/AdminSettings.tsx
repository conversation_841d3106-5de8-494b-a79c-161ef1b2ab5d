import { Favicon } from '@/components/community/favicon'
import { AnalyticsSettings } from '@/components/settings/admin-settings/AnalyticsSettings'
import { BillingAndReferralsSettings } from '@/components/settings/admin-settings/BillingAndReferrals'
import { GeneralSettings } from '@/components/settings/admin-settings/GeneralSettings'
import { InviteSettings } from '@/components/settings/admin-settings/InviteSettings'
import { LinksSettings } from '@/components/settings/admin-settings/LinksSettings'
import { ModerationSettings } from '@/components/settings/admin-settings/ModerationSettings'
import { PowerUps } from '@/components/settings/admin-settings/PowerUps'
import { PricingSettings } from '@/components/settings/admin-settings/PricingSettings'
import { SpacesSettings } from '@/components/settings/admin-settings/SpacesSettings'
import { NotificationsSettings } from '@/components/settings/member-settings/notification-settings'
import { SettingsModal, SettingsModalConfig } from '@/components/ui/settings-modal'
import { useStore } from '@/hooks/useStore'

export function AdminSettings({ open, onOpenChange }: { open: boolean; onOpenChange: (value: boolean) => void }) {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting

  if (!membership) {
    return
  }
  const config: SettingsModalConfig = {
    sections: [
      {
        name: 'general',
        title: 'General',
        component: GeneralSettings,
      },
      {
        name: 'spaces',
        title: 'Spaces',
        component: SpacesSettings,
      },
      {
        name: 'moderation',
        title: 'Moderation',
        component: ModerationSettings,
      },
      {
        name: 'power-ups',
        title: 'Power-ups',
        component: PowerUps,
      },
      {
        name: 'links',
        title: 'Links',
        component: LinksSettings,
      },
      {
        name: 'pricing',
        title: 'Pricing',
        component: PricingSettings,
      },
      {
        name: 'notifications',
        title: 'Notifications',
        component: NotificationsSettings,
      },
      {
        name: 'invite',
        title: 'Invite',
        component: InviteSettings,
      },
      {
        name: 'analytics',
        title: 'Analytics',
        component: AnalyticsSettings,
      },
      {
        name: 'billing',
        title: 'Billing & Referrals',
        component: BillingAndReferralsSettings,
      },
    ],
  }

  return (
    <SettingsModal
      header={
        <>
          <Favicon
            className="mr-3 h-12 w-12"
            communityName={membership.name}
            src={membershipSetting.favicon}
            cropArea={membershipSetting.favicon_crop_area}
            width={48}
            height={48}
            variant="inverted"
          />
          <div className="select-none">
            <h1 className="text-base font-semibold text-black-700 dark:text-white-500">{membership.name}</h1>
            <p className="text-ssm text-black-200 dark:text-black-100">Community settings</p>
          </div>
        </>
      }
      open={open}
      onOpenChange={onOpenChange}
      config={config}
      openFromUrl={true}
    />
  )
}
