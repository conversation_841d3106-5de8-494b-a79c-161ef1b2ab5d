import { isEqual } from 'lodash'
import { useMemo, useState } from 'react'

import ListMembershipQuestions from './membership-questions/list-membership-questions'
import MembershipQuestionEditor from './membership-questions/membership-question-editor'
import { Button } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { ScrollArea } from '@/components/ui/scroll-area'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { updateMembershipApi } from '@/shared-services/apis/membership.api'

export function MembershipQuestionsSettings({ onBackToPowerUpsClick }) {
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(null)
  const [mode, setMode] = useState('list')
  const [loading, setLoading] = useState(false)
  const membership = useStore((state) => state.community.membership)
  const setMembershipSettings = useStore((state) => state.community.setMembershipSettings)
  const membershipSetting = membership?.membership_setting
  const [deleteQuestionConfirmOpen, setDeleteQuestionConfirmOpen] = useState(false)
  const [formData, setFormData] = useState(membershipSetting.form || { fields: [] })
  const [formEnabled, setFormEnabled] = useState(membershipSetting.form_enabled)

  const hasChanges = useMemo(() => {
    if (!membershipSetting) return false
    const initialForm = membershipSetting.form || { fields: [] }
    const initialEnabled = membershipSetting.form_enabled

    // Compare form fields
    const formChanged = !isEqual(formData, initialForm)
    // Compare enabled state
    const enabledChanged = formEnabled !== initialEnabled

    return formChanged || enabledChanged
  }, [formData, formEnabled, membershipSetting])

  const handleOnEdit = (index) => {
    setSelectedQuestionIndex(index)
    setMode('edit')
  }

  const handleDeleteQuestionConfirmOnClose = () => {
    setDeleteQuestionConfirmOpen(false)
  }

  const handleDeleteQuestionConfirmOnProceed = async () => {
    setLoading(true)
    try {
      const newForm = JSON.parse(JSON.stringify(formData))
      newForm.fields.splice(selectedQuestionIndex, 1)
      setFormData(newForm)
      toast.success('Question deleted successfully')
      setDeleteQuestionConfirmOpen(false)
    } catch (error) {
      toast.error('Failed to delete question')
    }
    setLoading(false)
  }

  const handleOnAddQuestion = () => {
    setMode('add')
  }

  const handleOnSaveSuccess = () => {
    setMode('list')
    setSelectedQuestionIndex(null)
  }

  const handleOnCancel = () => {
    setMode('list')
    setSelectedQuestionIndex(null)
  }

  const handleSaveChanges = async () => {
    setLoading(true)
    try {
      const res = await updateMembershipApi(
        {
          form: formData,
          form_enabled: formEnabled,
        },
        membership.id,
      )
      setMembershipSettings(res.data.data)
      toast.success('Changes saved successfully')
    } catch (error) {
      toast.error('Failed to save changes')
    }
    setLoading(false)
  }

  const handleCancelChanges = () => {
    setFormData(membershipSetting.form || { fields: [] })
    setFormEnabled(membershipSetting.form_enabled)
  }

  return (
    <div className="absolute left-0 top-0 flex h-full w-full flex-col">
      <ScrollArea className="h-full flex-1">
        <div className="p-8">
          {['edit', 'add'].includes(mode) ? (
            <MembershipQuestionEditor
              mode={mode}
              selectedQuestionIndex={selectedQuestionIndex}
              onCancel={handleOnCancel}
              onSaveSuccess={handleOnSaveSuccess}
              onBackToPowerUpsClick={onBackToPowerUpsClick}
              formData={formData}
              setFormData={setFormData}
            />
          ) : (
            <ListMembershipQuestions
              onEdit={handleOnEdit}
              onAddQuestion={handleOnAddQuestion}
              onBackToPowerUpsClick={onBackToPowerUpsClick}
              formData={formData}
              setFormData={setFormData}
              formEnabled={formEnabled}
              setFormEnabled={setFormEnabled}
              loading={loading}
            />
          )}
        </div>
      </ScrollArea>
      {hasChanges && (
        <div className="flex justify-end space-x-4 px-8 py-4">
          <Button type="button" variant="outline" onClick={handleCancelChanges} data-cy="cancel-button">
            Cancel
          </Button>
          <Button variant="default" onClick={handleSaveChanges} loading={loading}>
            Save Changes
          </Button>
        </div>
      )}
      <ConfirmModal
        open={deleteQuestionConfirmOpen}
        onCancel={handleDeleteQuestionConfirmOnClose}
        onConfirm={handleDeleteQuestionConfirmOnProceed}
        title="Delete Question"
        loading={loading}
      >
        Are you sure you want to delete this question?
      </ConfirmModal>
    </div>
  )
}
