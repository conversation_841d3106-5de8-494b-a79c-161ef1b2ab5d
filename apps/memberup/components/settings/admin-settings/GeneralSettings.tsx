import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation'
import React, { useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { CoverPictureInput } from '@/components/profile/cover-picture-input'
import { ProfilePictureInput } from '@/components/profile/profile-picture-input'
import { Button, Input } from '@/components/ui'
import { BoxesRadioGroup } from '@/components/ui/boxes-radio-group'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { Form, FormControl, FormCounter, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { ColorInput } from '@/components/ui/input/ColorInput'
import { toast } from '@/components/ui/sonner'
import useMounted from '@/hooks/useMounted'
import { useStore } from '@/hooks/useStore'
import { communitySlugSchema } from '@/lib/validation/community'
import { updateMembershipApi } from '@/shared-services/apis/membership.api'
import { getCommunityBaseURL } from '@/src/libs/utils'

const COMMUNITY_NAME_MAX_LENGTH = 30
const MAX_DESCRIPTION_LENGTH = 70

export function GeneralSettings() {
  const router = useRouter()
  const params = useParams()
  const mounted = useMounted()
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const setMembership = useStore((state) => state.community.setMembership)
  const [isSaving, setIsSaving] = useState(false)
  const [faviconData, setFaviconData] = useState(null)
  const [faviconRemoved, setFaviconRemoved] = useState(false)
  const [coverPictureData, setCoverPictureData] = useState(null)
  const [coverPictureRemoved, setCoverPictureRemoved] = useState(false)
  const [confirmResetSettingsOpen, setConfirmResetSettingsOpen] = useState(false)
  const [showNewCommunityURLRedirection, setShowNewCommunityURLRedirection] = useState(false)
  const [redirectTimeoutSeconds, setRedirectTimeoutSeconds] = useState(4)
  const redirectionIntervalRef = useRef(null)
  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)

  const generalSettingsSchema = z.object({
    name: z.string().max(COMMUNITY_NAME_MAX_LENGTH),
    description: z.string().max(MAX_DESCRIPTION_LENGTH),
    slug: communitySlugSchema,
    support_email: z.string().email().optional(),
    theme_main_color: z.string().optional(),
    visibility: z.string(),
  })

  type GeneralSettingsSchemaType = z.infer<typeof generalSettingsSchema>

  const getDefaultValues = () => ({
    name: membership.name || '',
    description: membershipSetting?.description || '',
    support_email: membershipSetting?.support_email || undefined,
    slug: membership.slug,
    theme_main_color: membershipSetting?.theme_main_color,
    visibility: membershipSetting.visibility,
  })

  const generalSettingsForm = useForm<GeneralSettingsSchemaType>({
    mode: 'onBlur',
    reValidateMode: 'onSubmit',
    defaultValues: getDefaultValues(),
    resolver: zodResolver(generalSettingsSchema),
  })

  const updateDataWithPictures = (obj: any) => {
    if (faviconData) {
      Object.assign(obj, faviconData)
    } else if (faviconRemoved) {
      obj.favicon = null
      obj.favicon_crop_area = null
      obj.favicon_file = null
    }

    if (coverPictureData) {
      Object.assign(obj, coverPictureData)
    } else if (coverPictureRemoved) {
      obj.cover_image = null
      obj.cover_image_crop_area = null
      obj.cover_image_file = null
    }
  }

  const handleOnCancel = () => {
    setConfirmResetSettingsOpen(true)
  }

  const onGeneralSettingsSubmit = async (formData: GeneralSettingsSchemaType) => {
    try {
      setIsSaving(true)
      const data = { ...formData }
      updateDataWithPictures(data)

      if (typeof faviconData?.favicon_file?.name === 'string') {
        const result = await uploadFileToCloudinaryApi(faviconData.favicon_file)
        data['favicon'] = result.data.secure_url as string
      }

      if (typeof coverPictureData?.cover_image_file?.name === 'string') {
        const result = await uploadFileToCloudinaryApi(coverPictureData.cover_image_file)
        data['cover_image'] = result.data.secure_url as string
      }

      const result = await updateMembershipApi(data, membership.id)

      if (result.data.success) {
        toast.success('Community settings updated successfully')
      }

      const updatedMembership = result.data.data.membership
      const updatedMembershipSettings = result.data.data.membership.membership_settings
      const updatedUserMembership = result.data.data.user_membership

      const { slug } = params

      if (slug !== updatedMembership.slug) {
        setShowNewCommunityURLRedirection(true)
        redirectionIntervalRef.current = setInterval(() => {
          setRedirectTimeoutSeconds((seconds) => {
            if (seconds <= 1) {
              clearInterval(redirectionIntervalRef.current)
              router.replace(getCommunityBaseURL(updatedMembership) + '?settings')
              setShowNewCommunityURLRedirection(false)
            }
            return seconds - 1
          })
        }, 1000)
      }

      // As the membership is also in the user membership we need to reload both.
      setMembership({
        ...updatedMembership,
      })
      updateUserMembership(updatedUserMembership)

      generalSettingsForm.reset({
        name: updatedMembership.name,
        description: updatedMembershipSettings.description || '',
        slug: updatedMembership.slug,
        support_email: updatedMembership.support_email,
        visibility: updatedMembershipSettings.visibility,
        theme_main_color: updatedMembershipSettings.theme_main_color,
      })

      setFaviconData(null)
      setCoverPictureData(null)
    } catch (err) {
      console.log(err)
    } finally {
      setIsSaving(false)
    }
  }

  const onProfilePictureChange = (image, cropArea, file) => {
    setFaviconRemoved(false)
    setFaviconData({
      favicon: image,
      favicon_crop_area: cropArea,
      favicon_file: file,
    })
  }

  const onProfilePictureRemove = () => {
    setFaviconRemoved(true)
    setFaviconData(null)
  }

  const onCoverPictureChange = (image, cropArea, file) => {
    setCoverPictureRemoved(false)
    setCoverPictureData({
      cover_image: image,
      cover_image_crop_area: cropArea,
      cover_image_file: file,
    })
  }

  const onCoverPictureRemove = () => {
    setCoverPictureRemoved(true)
    setCoverPictureData(null)
  }

  const resetSettings = () => {
    generalSettingsForm.reset(getDefaultValues())
    setConfirmResetSettingsOpen(false)
    setFaviconData(null)
    setFaviconRemoved(false)
    setCoverPictureData(null)
    setCoverPictureRemoved(false)
  }

  const saveEnabled =
    !isSaving &&
    (generalSettingsForm.formState.isDirty || faviconRemoved || coverPictureRemoved || faviconData || coverPictureData)

  const onColorInputChange = (color) => {
    generalSettingsForm.setValue(
      'theme_main_color',
      `rgba(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b}, ${color.rgb.a})`,
      { shouldDirty: true },
    )
  }

  const newCommunityURLRedirectionMessage = `You are about to be redirected to your new community URL in ${redirectTimeoutSeconds} seconds...`

  return (
    <div className="space-y-6">
      <ConfirmModal
        title="New community URL redirection"
        open={showNewCommunityURLRedirection}
        onCancel={() => setShowNewCommunityURLRedirection(false)}
      >
        {newCommunityURLRedirectionMessage}
      </ConfirmModal>

      <h2 className="text-lg font-semibold text-black-700 dark:text-white-500">Community Settings</h2>
      <Form {...generalSettingsForm}>
        <div className="xl:flex xl:space-x-4">
          <div className="xl:w-6/12">
            <ProfilePictureInput
              className="mb-10"
              cropShape="rect"
              onSelectPicture={onProfilePictureChange}
              onRemovePicture={onProfilePictureRemove}
              src={faviconData?.favicon ?? (!faviconRemoved ? membershipSetting?.favicon : null)}
              cropArea={
                faviconData?.favicon_crop_area ?? (!faviconRemoved ? membershipSetting?.favicon_crop_area : null)
              }
              alt={membership.name}
              variant="favicon"
            />
            <FormField
              control={generalSettingsForm.control}
              name="theme_main_color"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <ColorInput
                      className="w-full"
                      disabled={isSaving}
                      type="text"
                      placeholder="Primary color"
                      error={Boolean(error)}
                      onChange={onColorInputChange}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="mb-6 mt-6 xl:mb-0 xl:mt-0 xl:w-6/12">
            <CoverPictureInput
              alt={`${membership.name} cover photo`}
              src={coverPictureData?.cover_image ?? (!coverPictureRemoved ? membershipSetting?.cover_image : null)}
              cropArea={
                coverPictureData?.cover_image_crop_area ??
                (!coverPictureRemoved ? membershipSetting?.cover_image_crop_area : null)
              }
              disabled={isSaving}
              onRemovePicture={onCoverPictureRemove}
              onSelectPicture={onCoverPictureChange}
            />
          </div>
        </div>
        <FormField
          control={generalSettingsForm.control}
          name="name"
          render={({ field, fieldState: { error } }) => (
            <FormItem>
              <FormControl>
                <Input
                  className="w-full"
                  disabled={isSaving}
                  type="text"
                  placeholder="Community Name"
                  error={Boolean(error)}
                  {...field}
                  maxLength={COMMUNITY_NAME_MAX_LENGTH}
                />
              </FormControl>
              <FormCounter>
                {generalSettingsForm.getValues('name').length}/{COMMUNITY_NAME_MAX_LENGTH}
              </FormCounter>
            </FormItem>
          )}
        />
        <FormField
          control={generalSettingsForm.control}
          name="description"
          render={({ field, fieldState: { error } }) => (
            <FormItem>
              <FormControl>
                <Input
                  disabled={isSaving}
                  type="text"
                  placeholder="Community Description"
                  error={Boolean(error)}
                  {...field}
                  maxLength={MAX_DESCRIPTION_LENGTH}
                />
              </FormControl>
              <FormCounter>
                {generalSettingsForm.getValues('description')?.length}/{MAX_DESCRIPTION_LENGTH}
              </FormCounter>
            </FormItem>
          )}
        />
        <FormField
          control={generalSettingsForm.control}
          name="slug"
          render={({ field, fieldState: { error } }) => (
            <FormItem>
              <FormControl>
                <Input
                  className="[&_input]:max-w-[45%]"
                  inputClassName="mx-px"
                  placeholder="URL"
                  error={Boolean(error)}
                  disabled={isSaving}
                  prepend={
                    <div className="mx-0 mr-px flex h-11 max-w-[55%] shrink overflow-hidden bg-transparent pl-4 text-sm leading-[2.75rem] text-grey-700 dark:text-grey-700">
                      <div className="overflow-hidden overflow-ellipsis whitespace-nowrap">
                        {mounted && window.location.origin}
                      </div>
                      <div>/</div>
                    </div>
                  }
                  {...field}
                />
              </FormControl>
              {error && <FormMessage>{error.message}</FormMessage>}
            </FormItem>
          )}
        />
        <FormField
          control={generalSettingsForm.control}
          name="support_email"
          render={({ field, fieldState: { error } }) => (
            <FormItem>
              <FormControl>
                <Input disabled={isSaving} type="text" placeholder="Support Email" error={Boolean(error)} {...field} />
              </FormControl>
              {error && <FormMessage>{error.message}</FormMessage>}
            </FormItem>
          )}
        />
        <BoxesRadioGroup
          options={[
            {
              label: 'Public',
              value: 'public',
              description: 'Anyone can view group members and posts. Content is discoverable by search engines.',
            },
            {
              label: 'Private',
              value: 'private',
              description: 'Only members can view group members and posts. Content is hidden in search engines.',
            },
          ]}
          value={generalSettingsForm.getValues('visibility')}
          onValueChange={(value) => generalSettingsForm.setValue('visibility', value, { shouldDirty: true })}
        />
        <div className={'flex flex-row justify-end gap-2'}>
          {saveEnabled && (
            <Button type="button" variant="outline" onClick={handleOnCancel} data-cy="cancel-button">
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            variant="default"
            loading={isSaving}
            disabled={!saveEnabled}
            onClick={generalSettingsForm.handleSubmit(onGeneralSettingsSubmit)}
            data-cy="save-changes-button"
          >
            Save Changes
          </Button>
        </div>
      </Form>
      <ConfirmModal
        title="Are you sure you want to discard your changes?"
        onConfirm={resetSettings}
        open={confirmResetSettingsOpen}
        onCancel={() => setConfirmResetSettingsOpen(false)}
      />
    </div>
  )
}
