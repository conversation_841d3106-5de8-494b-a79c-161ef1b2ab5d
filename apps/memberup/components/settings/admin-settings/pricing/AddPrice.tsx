import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Switch } from '@/components/ui'
import { Form, FormControl, FormCounter, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { paymentTypes } from '@/lib/constants'
import { getAddPaymentErrorMessage } from '@/lib/error-messages'
import { createStripePriceApi } from '@/shared-services/apis/stripe.api'
import { IMembership } from '@/shared-types/interfaces'

const TITLE_MAX_LENGTH = 30

interface AddPriceProps {
  membership: IMembership
  onBack: () => void
  onCancel: () => void
  onSuccess: () => void
}

export default function AddPrice({ membership, onBack, onCancel, onSuccess }: AddPriceProps) {
  const [saving, setSaving] = useState(false)
  const setMembershipSettings = useStore((state) => state.community.setMembershipSettings)

  const priceSchema = z.object({
    title: z.string().max(TITLE_MAX_LENGTH).min(1),
    price: z.string().min(1, { message: 'Price is required' }), // Require a price value
    payment_type: z.enum(['one-time', 'year', 'month']),
    is_default: z.boolean().optional(), // New set_as_default boolean
  })

  type PriceSchemaType = z.infer<typeof priceSchema>
  const priceForm = useForm<PriceSchemaType>({
    mode: 'onChange',
    reValidateMode: 'onSubmit',
    defaultValues: {
      title: '',
      price: '',
      payment_type: 'month', // Set monthly as default
      is_default: false,
    },
    resolver: zodResolver(priceSchema),
  })

  const onSubmit = async (data: PriceSchemaType) => {
    try {
      setSaving(true)
      const response = await createStripePriceApi(true, membership.id, data)
      if (response?.data?.data?.membership_setting) {
        setMembershipSettings(response.data.data.membership_setting)
        toast.success('Price created successfully')
      }
    } catch (error) {
      const errorCode = error.response?.data?.error?.code
      toast.error(getAddPaymentErrorMessage(errorCode))
    } finally {
      setSaving(false)
      onSuccess()
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
  }

  return (
    <div>
      <h2 className="text-lg font-semibold text-white-500">Add Price</h2>
      <div className="mb-8 w-fit cursor-pointer text-sm font-medium leading-normal text-primary-100" onClick={onBack}>
        Back to pricing
      </div>
      <Form {...priceForm}>
        <div className={'flex w-full flex-col space-y-6'}>
          <FormField
            control={priceForm.control}
            name="title"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    maxLength={TITLE_MAX_LENGTH}
                    disabled={saving}
                    type="text"
                    placeholder="Title"
                    error={Boolean(error)}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
                <FormCounter>
                  {priceForm.getValues('title').length}/{TITLE_MAX_LENGTH}
                </FormCounter>
              </FormItem>
            )}
          />
          <FormField
            control={priceForm.control}
            name="payment_type"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Select
                    className="w-full"
                    placeholder="Payment Type"
                    error={Boolean(error)}
                    value={field.value}
                    name={field.name}
                    onValueChange={field.onChange}
                    disabled={saving}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue onBlur={field.onBlur} ref={field.ref} />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />
          <FormField
            control={priceForm.control}
            name="price"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    maxLength={TITLE_MAX_LENGTH}
                    disabled={saving}
                    type="text"
                    placeholder="Price (required)"
                    error={Boolean(error)}
                    {...field}
                    onChange={(e) => {
                      // Only allow numbers and decimal point
                      const value = e.target.value.replace(/[^0-9.]/g, '')
                      // Ensure only one decimal point
                      const parts = value.split('.')
                      const formattedValue = parts.length > 2 ? `${parts[0]}.${parts.slice(1).join('')}` : value
                      field.onChange(formattedValue)
                    }}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />
          <div className={'flex'}>
            <div className={'grow'}>
              <div className="text-white text-sm font-medium leading-none">
                <div className={'flex flex-col space-y-2'}>
                  <div className={'flex items-center gap-4'}>
                    <FormField
                      control={priceForm.control}
                      name="is_default"
                      render={({ field: { value } }) => (
                        <FormItem>
                          <FormControl>
                            <Switch
                              checked={value}
                              onClick={() => priceForm.setValue('is_default', !value, { shouldDirty: true })}
                            ></Switch>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    Set as default
                  </div>
                </div>
              </div>
            </div>
            <div className={'flex flex-row gap-2'}>
              <Button type="button" variant="outline" disabled={saving} onClick={() => handleCancel()}>
                Cancel
              </Button>
              <Button
                type="submit"
                variant="default"
                loading={saving}
                disabled={
                  saving ||
                  !priceForm.formState.isValid ||
                  !priceForm.formState.isDirty ||
                  !priceForm.getValues('price')
                }
                onClick={priceForm.handleSubmit(onSubmit)}
              >
                Add
              </Button>
            </div>
          </div>
        </div>
      </Form>
    </div>
  )
}
