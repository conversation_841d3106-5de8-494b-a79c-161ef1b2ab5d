'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { ProfileEditorForm, UserDetails } from '@/components/profile'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useRouteProtection } from '@/hooks/useRouteProtection'
import { useUpdateUserProfile } from '@/hooks/users/useUpdateUserProfile'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { nameSchema, usernameSchema } from '@/lib/validation/user'
import { userProfileSchema } from '@/lib/validation/user-profile'
import { IUser } from '@/shared-types/interfaces'
import { CropArea } from '@/shared-types/types'

const formSchema = nameSchema.merge(usernameSchema).merge(userProfileSchema)

export type EditProfileSchemaType = z.infer<typeof formSchema>

export function UserProfileSettings() {
  const user = useStore((state) => state.auth.user)
  const profile = useStore((state) => state.auth.profile)
  const { saving, updateUserProfile } = useUpdateUserProfile()
  const [editingName, setEditingName] = useState(false)
  const [editingUsername, setEditingUsername] = useState(false)
  const [profilePictureData, setProfilePictureData] = useState(null)
  const [coverPictureData, setCoverPictureData] = useState(null)
  const [coverPictureRemoved, setCoverPictureRemoved] = useState(false)
  const [formSaving, setFormSaving] = useState(false)

  useRouteProtection()

  const getDefaultValues = () => ({
    first_name: user?.first_name,
    last_name: user?.last_name,
    username: user?.username,
    bio: profile?.bio || '',
    personality_type: profile?.personality_type || 'DS',
    location: profile?.location || '',
    social: {
      website: profile?.social?.website || '',
      facebook: profile?.social?.facebook || '',
      instagram: profile?.social?.instagram || '',
      youtube: profile?.social?.youtube || '',
      linkedin: profile?.social?.linkedin || '',
      tiktok: profile?.social?.tiktok || '',
      x: profile?.social?.x || '',
    },
  })

  const form = useForm<EditProfileSchemaType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: getDefaultValues(),
    resolver: zodResolver(formSchema),
  })

  const formData = form.getValues()

  const updateDataWithPictures = (obj: any) => {
    if (profilePictureData) {
      Object.assign(obj, profilePictureData)
    }

    if (coverPictureData) {
      Object.assign(obj, coverPictureData)
    } else if (coverPictureRemoved) {
      obj.cover_image = null
      obj.cover_image_crop_area = null
    }
  }

  const onSubmit = async (values: EditProfileSchemaType) => {
    setFormSaving(true)
    const data = { ...values } as any

    updateDataWithPictures(data)
    const success = await updateUserProfile(data)

    if (success) {
      toast.success('Profile updated successfully')
      setProfilePictureData(null)
      setCoverPictureData(null)
      setCoverPictureRemoved(false)
      form.reset(values)
      setFormSaving(false)
    } else {
      toast.error(formSubmitError)
      setFormSaving(false)
    }
  }

  const onChangeCoverPicture = (image: string, cropArea: CropArea, file: any) => {
    setCoverPictureData({
      cover_image: image,
      cover_image_crop_area: cropArea,
      cover_image_file: file,
    })
  }

  const onChangeProfilePicture = (image: string, cropArea: CropArea, file: any) => {
    setProfilePictureData({
      image: image,
      image_crop_area: cropArea,
      image_file: file,
    })
  }

  const onRemoveCoverPicture = () => {
    setCoverPictureData(null)
    setCoverPictureRemoved(true)
  }

  const detailsData = {
    ...user,
    first_name: formData.first_name,
    last_name: formData.last_name,
    profile: { ...profile, ...formData },
  } as IUser

  updateDataWithPictures(detailsData.profile)

  const buttonDisabled =
    formSaving || (!form.formState.isDirty && !profilePictureData && !coverPictureData && !coverPictureRemoved)

  return (
    <div className="user-profile tailwind-component space-y:6 page-inner-pb flex flex-col items-start md:flex-row md:space-x-6 md:space-y-0">
      <div className="rounded-box w-full max-w-full shrink grow overflow-y-auto overflow-x-hidden p-5 md:w-auto">
        <h2 className="-mt-1 mb-4 text-lg font-semibold">Profile settings</h2>
        <ProfileEditorForm
          editingName={editingName}
          editingUsername={editingUsername}
          setEditingName={setEditingName}
          setEditingUsername={setEditingUsername}
          form={form}
          onCoverPictureChange={onChangeCoverPicture}
          onChangeProfilePicture={onChangeProfilePicture}
          onRemoveCoverPicture={onRemoveCoverPicture}
          saving={saving}
          user={{ ...user, profile: profile }}
          updatedUser={detailsData}
        />
      </div>
      <div className="sticky mb-4 w-full shrink-0 md:mb-0 md:w-72 xl:w-[19rem]">
        <UserDetails className="hidden md:block" user={detailsData}>
          <Button
            className="w-full"
            variant={saving || buttonDisabled ? 'disabled' : 'default'}
            onClick={form.handleSubmit(onSubmit)}
            disabled={saving || buttonDisabled}
            loading={saving}
          >
            Update Profile
          </Button>
        </UserDetails>
      </div>
      <Button
        className="w-full md:hidden"
        variant={saving || buttonDisabled ? 'disabled' : 'default'}
        onClick={form.handleSubmit(onSubmit)}
        disabled={saving || buttonDisabled}
        loading={saving}
      >
        Update Profile
      </Button>
    </div>
  )
}
