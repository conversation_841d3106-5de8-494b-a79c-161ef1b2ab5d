'use client'

import { useEffect, useRef } from 'react'
import { Event, StreamChat } from 'stream-chat'
import { Chat } from 'stream-chat-react'

import { GET_STREAM_APP_KEY } from '@memberup/shared/src/config/envs'
import { CHANNEL_TYPE_ENUM, USER_STATUS_ENUM, VISIBILITY_ENUM } from '@memberup/shared/src/types/enum'
import { useStore } from '@/hooks/useStore'
import { getStreamChatClient } from '@/memberup/libs/getstream'
import { getMembers } from '@/memberup/store/features/memberSlice'
import { useAppDispatch } from '@/memberup/store/hooks'

export function StreamChatProvider({ children }: { children: React.ReactNode }): React.ReactNode {
  const dispatch = useAppDispatch()

  const user = useStore((state) => state.auth.user)
  const streamChatUserToken = useStore((state) => state.auth.streamChatUserToken)
  const setConnected = useStore((state) => state.streamChat.setConnected)
  const setConnecting = useStore((state) => state.streamChat.setConnecting)
  const connected = useStore((state) => state.streamChat.connected)
  const fetchChannels = useStore((state) => state.notifications.fetchChannels)

  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership?.membership_setting)
  const userMembership = useStore((state) =>
    membership ? state.auth.user?.user_memberships.find((um) => um.membership.id === membership.id) : null,
  )
  const gettingForMembershipRef = useRef(null)
  const gettingForUserRef = useRef(null)

  // Get the singleton StreamChat instance
  const chatClient = getStreamChatClient()

  // Immediately set connected to false when user state changes require reconnection
  useEffect(() => {
    if (!chatClient) return

    const currentStreamUserId = chatClient.user?.id
    const isAnonymous = currentStreamUserId?.startsWith('!anon') || chatClient.user?.anon === true

    // Set connected to false and connecting to true immediately if:
    // 1. User logged out but stream client still has non-anonymous user
    // 2. User logged in but stream client doesn't have the correct user
    if ((!user && currentStreamUserId && !isAnonymous) || (user && currentStreamUserId !== user.id)) {
      setConnected(false)
      setConnecting(true)
    }
  }, [user, setConnected, setConnecting, chatClient])

  useEffect(() => {
    const connectAnonymousUser = async () => {
      await chatClient.connectAnonymousUser()
      setConnected(true)
      setConnecting(false)
    }

    const connectAuthenticatedUser = async () => {
      await chatClient.connectUser({ id: user.id }, streamChatUserToken)
      setConnected(true)
      setConnecting(false)
    }

    const connectOrReconnect = async () => {
      if (!chatClient) return

      let connect = false
      const currentStreamUserId = chatClient.user?.id
      const isAnonymous = currentStreamUserId?.startsWith('!anon') || chatClient.user?.anon === true

      if (!user && currentStreamUserId && !isAnonymous) {
        // User logged out but stream client still has authenticated (non-anonymous) user
        connect = true
        setConnected(false)
        setConnecting(true)

        await chatClient.disconnectUser()
      } else if (user && streamChatUserToken && (!currentStreamUserId || currentStreamUserId !== user.id)) {
        // User logged in but stream client doesn't have the correct user
        connect = true
        setConnected(false)
        setConnecting(true)

        if (currentStreamUserId) {
          await chatClient.disconnectUser()
        }
      } else if (!user && !currentStreamUserId) {
        // No user and no stream user - need to connect anonymously
        connect = true
        setConnected(false)
        setConnecting(true)
      }

      if (connect) {
        if (!user) {
          connectAnonymousUser()
        } else {
          connectAuthenticatedUser()
        }
      }
    }

    connectOrReconnect()
  }, [setConnecting, setConnected, streamChatUserToken, user, chatClient])

  useEffect(() => {
    if (!membership) {
      return
    }

    if (
      (!gettingForMembershipRef.current ||
        (membership && gettingForMembershipRef.current !== membership.id) ||
        gettingForUserRef.current !== user?.id) &&
      (membershipSetting?.visibility === VISIBILITY_ENUM.public ||
        (userMembership && userMembership.status === 'accepted'))
    ) {
      dispatch(
        getMembers({
          membershipId: membership.id,
          where: JSON.stringify({
            status: USER_STATUS_ENUM.active,
          }),
          take: 10000,
          skip: 0,
        }),
      )
      gettingForMembershipRef.current = membership.id
      gettingForUserRef.current = user?.id
    } else if ((gettingForMembershipRef.current && !membership) || (!user && gettingForUserRef.current)) {
      gettingForMembershipRef.current = null
      gettingForUserRef.current = null
    }
  }, [user, membership, dispatch, membershipSetting, userMembership])

  useEffect(() => {
    if (!chatClient || !connected || !user) {
      return
    }

    const handleNewInboxMessage = async (event: Event) => {
      if (event.channel_type !== 'messaging') return
      await fetchChannels()
    }

    fetchChannels()

    chatClient.on('message.new', handleNewInboxMessage)

    return () => {
      chatClient.off('message.new', handleNewInboxMessage)
    }
  }, [connected, user, fetchChannels, chatClient])

  return (
    <div>
      <Chat
        client={chatClient}
        theme="messaging"
        customClasses={{
          chat: 'custom-chat-class',
          channel: 'custom-channel-class',
        }}
      >
        {children}
      </Chat>
    </div>
  )
}
