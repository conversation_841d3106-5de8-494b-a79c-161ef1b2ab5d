'use client'

import { RadioDropdown } from '../ui/radio-dropdown'
import { SkeletonBox } from '@/components/ui/skeleton'
import { useStore } from '@/hooks/useStore'
import { filterSpaces } from '@/lib/utils'

const viewOptions = [
  { label: 'View All', value: 'all' },
  { label: 'Unread', value: 'unread' },
]

const orderByOptions = [
  { label: 'Activity', value: 'activity' },
  { label: 'Newest', value: 'newest' },
]

interface FeedFiltersProps {
  onSortByChange: (sortBy: string) => void
  onSpaceChange: (spaceId: string) => void
  sortBy: string
  spaceConfig: string
  disabled: boolean
}

export function FeedFilters({ onSortByChange, onSpaceChange, sortBy, spaceConfig, disabled }: FeedFiltersProps) {
  const membership = useStore((state) => state.community.membership)
  const userCanManageCommunity = useStore((state) => state.community.userCanManageCommunity)
  if (!membership || !spaceConfig)
    return (
      <div className="my-4 flex">
        <SkeletonBox className="mr-2 h-10 w-20" />
        <SkeletonBox className="h-10 w-20" />
      </div>
    )

  const filteredSpaces = filterSpaces(membership?.channels, userCanManageCommunity)
  const currentSpace = filteredSpaces.find((s) => s.id === spaceConfig)
  const sortLabel = `Sort: ${orderByOptions.find((opt) => opt.value === sortBy)?.label}`
  const spaceLabel = `Space: ${spaceConfig === 'community' ? 'All spaces' : currentSpace?.name}`

  const spacesListing = [
    {
      value: 'community',
      label: 'All spaces',
    },
    ...(filteredSpaces?.map((space) => ({
      label: space.name,
      value: space.id,
    })) || []),
  ]

  return (
    <div className="my-4 flex flex-col gap-4 sm:flex-row">
      {/*<RadioDropdown className="mr-4" label={filterLabel} items={viewOptions} onChange={() => {}} value={null} />*/}
      <RadioDropdown
        label={sortLabel}
        items={orderByOptions}
        onValueChange={onSortByChange}
        value={sortBy}
        disabled={disabled}
      />
      <RadioDropdown
        label={spaceLabel}
        items={spacesListing}
        onValueChange={onSpaceChange}
        value={spaceConfig}
        disabled={disabled}
      />
    </div>
  )
}
