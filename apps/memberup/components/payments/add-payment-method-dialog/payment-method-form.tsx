'use client'

import { CheckedState } from '@radix-ui/react-checkbox'
import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js'
import type { PaymentMethod, StripeCardElementChangeEvent } from '@stripe/stripe-js'
import { useEffect, useState } from 'react'
import { useMediaQuery } from 'react-responsive'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { Checkbox } from '@/components/ui'
import { Button } from '@/components/ui/button'
import { DialogFooter, DialogInner } from '@/components/ui/dialog'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { smMediaQuery } from '@/lib/client/media-queries'
import { unexpectedError } from '@/lib/error-messages'
import { getStripeSecureReturnUrl } from '@/lib/stripe'
import { cn } from '@/lib/utils'
import { getStripePaymentMethodsApi } from '@/shared-services/apis/stripe.api'

interface PaymentMethodFormProps {
  paymentMethods: PaymentMethod[]
  onSuccess: (paymentMethod: PaymentMethod) => Promise<void>
  onCancel: () => void
  updateDefault: boolean
  setUpdateDefault?: (value: boolean) => void
  clientSecret: string
  fullHeight?: boolean
  isMembershipPaymentMethod: boolean
}

export function PaymentMethodForm({
  paymentMethods,
  onSuccess,
  onCancel,
  updateDefault,
  setUpdateDefault,
  clientSecret,
  fullHeight = false,
  isMembershipPaymentMethod,
}: PaymentMethodFormProps) {
  const elements = useElements()
  const stripe = useStripe()
  const mountedRef = useMounted(true)
  const isSm = useMediaQuery(smMediaQuery)
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)
  const [saving, setSaving] = useState(false)
  const [cardComplete, setCardComplete] = useState(false)
  const [cardInputEmpty, setCardInputEmpty] = useState(true)
  const [cardError, setCardError] = useState(false)
  const [isStripeReady, setIsStripeReady] = useState(false)
  const [fontSize, setFontSize] = useState<string | null>(null)

  useEffect(() => {
    setIsStripeReady(Boolean(stripe && elements))
  }, [stripe, elements])

  useEffect(() => {
    const getFontSize = () => (isSm ? '14px' : '16px')
    setFontSize(getFontSize())
    const handleResize = () => setFontSize(getFontSize())
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const handleCardChange = (event: StripeCardElementChangeEvent) => {
    if (event.empty !== cardInputEmpty) {
      setCardInputEmpty(event.empty)
    }
    if (event.complete !== cardComplete) {
      setCardComplete(event.complete)
    }
    if (Boolean(event.error) !== cardError) {
      setCardError(Boolean(event.error))
    }
  }

  const onSubmit = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
    setSaving(true)

    try {
      if (!stripe || !elements) {
        setSaving(false)
        return
      }

      const cardElement = elements.getElement(CardElement)

      if (!cardElement) {
        setSaving(false)
        return
      }

      const { setupIntent, error } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: {
          card: cardElement,
        },
        return_url: getStripeSecureReturnUrl(),
      })

      if (error) {
        toast.error(error.message || unexpectedError)
        setSaving(false)
      } else if (mountedRef.current && setupIntent?.payment_method) {
        if (isMembershipPaymentMethod) {
          await onSuccess({ id: setupIntent.payment_method } as PaymentMethod)
          setSaving(false)
        } else {
          const response = await getStripePaymentMethodsApi(false, { type: 'card' })
          const paymentMethods = response.data.data.data
          const fullPaymentMethod = paymentMethods.find((pm) => pm.id === setupIntent.payment_method)

          if (fullPaymentMethod) {
            await onSuccess(fullPaymentMethod)
            setSaving(false)
          }
        }
      }
      setSaving(false)
    } catch (err) {
      toast.error(err.message || unexpectedError)
      setSaving(false)
    }
  }

  if (!fontSize) return null

  return (
    <>
      <DialogInner className={cn(fullHeight && 'pl-0 pr-0 sm:pr-8')}>
        <div className={cn('space-y-4', fullHeight && 'h-[calc(100dvh-25rem)] sm:h-full')}>
          <CardElement
            className={cn(
              'rounded-base border border-black-700 bg-black-100/[0.08] px-4 [&_iframe]:focus:outline-none',
              cardError ? 'border-red-200' : cardInputEmpty ? 'border-black-100/[0.08]' : 'border-black-100',
            )}
            options={{
              hidePostalCode: false,
              style: {
                base: {
                  color: isDarkTheme ? '#F1F2F5' : '#1F2937',
                  lineHeight: '3.4285rem',
                  fontSize,
                  '::placeholder': {
                    color: '#8D94A3',
                  },
                },
                invalid: {
                  color: '#F34646',
                  iconColor: '#F34646',
                },
              },
            }}
            onChange={handleCardChange}
          />
        </div>
      </DialogInner>
      <div className="px-8">
        <div className={cn('flex items-center space-x-2')}>
          {!isMembershipPaymentMethod && paymentMethods && paymentMethods.length > 0 && (
            <div className="flex pb-8">
              <Checkbox
                id="set_as_default"
                checked={updateDefault}
                onCheckedChange={(checked: CheckedState) => setUpdateDefault?.(checked === true)}
              />
              <label
                htmlFor="set_as_default"
                className="cursor-pointer pl-1 text-sm font-medium leading-5 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Set as default
              </label>
            </div>
          )}
        </div>
      </div>
      <DialogFooter className={cn(fullHeight && 'pl-0 pr-0 sm:pr-8')}>
        <Button className="w-full sm:w-32" disabled={saving} onClick={onCancel} variant="outline">
          Cancel
        </Button>
        <Button
          className="w-full sm:w-32"
          disabled={saving || !cardComplete || !isStripeReady}
          loading={saving}
          type="submit"
          variant="default"
          onClick={onSubmit}
        >
          Save
        </Button>
      </DialogFooter>
    </>
  )
}
