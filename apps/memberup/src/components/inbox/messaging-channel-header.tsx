import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import CircleIcon from '@mui/icons-material/Circle'
import CloseIcon from '@mui/icons-material/Close'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import { Dialog, DialogContent, DialogTitle, List, ListItem, ListItemAvatar } from '@mui/material'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useMemo, useState } from 'react'
import { useChannelStateContext } from 'stream-chat-react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { showToast } from '@memberup/shared/src/libs/toast'
import { deleteStreamChatChannelApi } from '@memberup/shared/src/services/apis/stream-chat.api'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { useStore } from '@/hooks/useStore'
import { getChatChannelMembers } from '@/lib/stream'
import AppMessagingEditChannel from '@/memberup/components/dialogs/messaging/messaging-edit-channel'
import AppWarning from '@/memberup/components/dialogs/warning'
import SVGTrash from '@/memberup/components/svgs/trash'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    maxWidth: '450px',
    position: 'absolute',
    margin: 'auto',
  },
  dialogContent: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 320,
    width: '100%',
    margin: 'auto',
    padding: '12px',
  },
  dialogTitle: {
    borderBottom: 'none',
  },
}))

const ChannelTitle = ({ channelName, members }) => {
  const renderSubTitle = useMemo(() => {
    if (members.length > 1) {
      return (
        <Typography className="text-black-700 dark:text-white-500" variant="body2" sx={{ fontSize: 12, mt: '4px' }}>
          Group chat with {members.length} people
        </Typography>
      )
    }

    if (!members[0]?.last_active) return null

    const lastActive = members[0].last_active && new Date(members[0].last_active)

    if (!lastActive) return null

    let tempStr = ''
    const [lastActiveYear, lastActiveMonth, lastActiveDate] = formatDate({
      date: lastActive,
      format: "yyyy','L','d",
    }).split(',')
    const [nowYear, nowMonth, nowDate] = formatDate({
      date: new Date(),
      format: "yyyy','L','d",
    }).split(',')
    if (lastActiveYear === nowYear && lastActiveMonth === nowMonth && lastActiveDate === nowDate) {
      tempStr = formatDate({ date: lastActive, format: "'Today at' K:mm aaa" })
    } else if (
      lastActiveYear === nowYear &&
      lastActiveMonth === nowMonth &&
      parseInt(lastActiveDate) === parseInt(nowDate) - 1
    ) {
      tempStr = formatDate({ date: lastActive, format: "'Yesterday at' K:mm aaa" })
    } else if (lastActiveYear === nowYear) {
      tempStr = formatDate({ date: lastActive, format: "LLL d 'at' K:mm aaa" })
    } else {
      tempStr = formatDate({ date: lastActive, format: "LLL d yyyy 'at' K:mm aaa" })
    }

    if (!tempStr) return null

    return (
      <Typography color="text.disabled" variant="body2" sx={{ fontSize: 12, mt: '4px' }}>
        Last Active&nbsp;
        <CircleIcon sx={{ fontSize: 4, height: 8 }} />
        &nbsp;{tempStr}
      </Typography>
    )
  }, [members])

  if (channelName) {
    return (
      <Box sx={{ pt: '3px' }}>
        <Typography
          className="font-family-graphik-semibold text-ellipsis text-black-700 dark:text-white-500"
          data-cy="app-messaging-channel-name"
          sx={{ fontSize: 16 }}
        >
          {channelName}
        </Typography>
        {renderSubTitle}
      </Box>
    )
  }

  return (
    <Box>
      <Box className="text-ellipsis" sx={{ pt: '3px' }}>
        {members.map((member: any, index: any) => {
          return (
            <span key={member?.id}>
              {index > 0 && (
                <Typography
                  className="font-family-graphik-semibold text-black-700 dark:text-white-500"
                  component="span"
                  sx={{ fontSize: 16 }}
                >
                  ,&nbsp;
                </Typography>
              )}
              <UserDetailsHoverCard username={member.username}>
                <Typography
                  className="font-family-graphik-semibold text-black-700 dark:text-white-500"
                  component="span"
                  data-cy="app-messaging-channel-name"
                  sx={{ fontSize: 16, cursor: member?.id ? 'pointer' : undefined }}
                >
                  {member?.name || 'Unnamed User'}
                </Typography>
              </UserDetailsHoverCard>
            </span>
          )
        })}
      </Box>
      {renderSubTitle}
    </Box>
  )
}

const AppMessagingChannelHeader: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const userCanManageCommunity = useStore((state) => state.community.userCanManageCommunity)
  const user = useStore((state) => state.auth.user)
  const theme = useTheme()
  const { channel } = useChannelStateContext()
  const [moreAnchorEl, setMoreAnchorEl] = useState(null)
  const [openEditChannel, setOpenEditChannel] = useState(false)
  const [openDeleteWarning, setOpenDeleteWarning] = useState(false)
  const [requestDeleteChannel, setRequestDeleteChannel] = useState(false)
  const isLightTheme = theme.palette.mode === THEME_MODE_ENUM.light
  const canEditChannel =
    userCanManageCommunity ||
    (channel.data.created_by?.['id'] && channel.data.created_by['id'] === channel._client.user.id)
  const members = getChatChannelMembers(channel)
  const involvedUsers = members.map((member) => member.id)

  const [openViewMembers, setOpenViewMembers] = useState(false)
  const classes = useStyles()
  const handleViewMembers = () => {
    setOpenViewMembers(true)
    setMoreAnchorEl(null)
  }

  const MembersPopup = () => (
    <Dialog onClose={() => setOpenViewMembers(false)} open={openViewMembers} fullWidth className={classes.root}>
      <DialogTitle className="bg-white-500 text-black-700 dark:bg-black-500 dark:text-white-500">
        Members
        <IconButton
          aria-label="close"
          onClick={() => setOpenViewMembers(false)}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        sx={{
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          cursor: 'pointer',
          maxHeight: '400px',
          overflowY: 'auto',
        }}
        className="bg-white-500 dark:bg-black-500"
      >
        <List sx={{ pt: 0, mt: 'auto' }}>
          {members.map((member) => {
            return (
              <React.Fragment key={member.id}>
                <ListItem
                  alignItems="center"
                  onClick={() => member.id}
                  sx={{
                    padding: '16px',
                    borderRadius: '12px',
                    marginBottom: '-10px',
                    cursor: 'pointer',
                  }}
                >
                  <ListItemAvatar sx={{ marginLeft: '-5px' }}>
                    <AppProfileImage imageUrl={member.image} cropArea={member.image_crop_area} name={member.name} />
                  </ListItemAvatar>
                  <ListItemText
                    style={{ marginLeft: '-14px' }}
                    primary={
                      <Typography className="text-black-700 dark:text-white-500">
                        {member.name || 'Unnamed User'}
                      </Typography>
                    }
                  />
                </ListItem>
              </React.Fragment>
            )
          })}
        </List>
      </DialogContent>
    </Dialog>
  )

  const handleDeleteChannel = () => {
    try {
      setOpenDeleteWarning(false)
      setRequestDeleteChannel(true)
      let deleteChannelApi
      if (canEditChannel || involvedUsers.length <= 2) {
        deleteChannelApi = deleteStreamChatChannelApi(channel.cid)
      } else {
        deleteChannelApi = channel.removeMembers([user.id])
      }
      deleteChannelApi
        .then((res) => {})
        .catch((err) => {
          showToast(canEditChannel ? 'Failed to delete channel' : 'Failed to leave channel', 'error')
        })
        .finally(() => {
          setRequestDeleteChannel(false)
          onClose()
        })
    } catch (err) {}
  }

  const handleClickMore = (e) => {
    e.stopPropagation()
    e.preventDefault()
    setMoreAnchorEl(e.currentTarget)
  }

  const onDeleteChannel = (e) => {
    e.stopPropagation()
    e.preventDefault()
    setOpenDeleteWarning(true)
    setMoreAnchorEl(null)
  }

  const onEditChannel = (e) => {
    e.stopPropagation()
    e.preventDefault()
    setOpenEditChannel(true)
    setMoreAnchorEl(null)
  }

  const renderAvatar = useMemo(() => {
    if (channel.data?.image || members.length < 2) {
      return (
        <Box sx={{ width: 40, height: 40, position: 'relative', mr: '12px' }}>
          <AppProfileImage
            imageUrl={members[0].image || channel.data?.image}
            cropArea={(members[0].image_crop_area as any) || !channel.data?.image}
            name={involvedUsers[0].name || ''}
            size={40}
            fontSize={18}
          />
        </Box>
      )
    }

    return (
      <Box sx={{ width: 40, height: 40, position: 'relative', mr: '12px' }}>
        <AppProfileImage
          imageUrl={members[0].image}
          cropArea={members[0].image && (members[0].image_crop_area as any)}
          name={members[0].name || ''}
          size={26}
          fontSize={16}
        />
        <Box
          sx={{
            width: 26,
            height: 26,
            position: 'absolute',
            bottom: 2,
            right: 2,
            borderStyle: 'solid',
            borderColor: theme.palette.background.paper,
            borderRadius: '50%',
            borderWidth: 2,
          }}
        >
          <AppProfileImage
            imageUrl={members[1].image}
            cropArea={members[1].image && (members[1].image_crop_area as any)}
            name={members[1].name || ''}
            size={26}
            fontSize={16}
          />
        </Box>
      </Box>
    )
  }, [channel.data?.image, involvedUsers])

  return (
    <Box
      className="bg-white-500 dark:bg-black-500"
      sx={{
        borderBottom: isLightTheme ? `1px solid rgba(111, 118, 133, 0.12)` : `1px solid rgba(141, 148, 163, 0.12)`,
        padding: { xs: '8px 8px', sm: '16px 16px' },
      }}
      data-cy="app-messaging-channel-header"
    >
      <Grid container overflow="hidden">
        <Grid item sx={{ display: { xs: 'block', sm: 'none' }, pt: '6px' }}>
          <IconButton size="small" onClick={onClose}>
            <ArrowBackIosNewIcon fontSize="small" />
          </IconButton>
          &nbsp;
        </Grid>
        <Grid item>{renderAvatar}</Grid>
        <Grid item xs overflow="hidden">
          <ChannelTitle channelName={channel?.data.name} members={members} />
        </Grid>
        <Grid item sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton
            size="small"
            aria-label="more"
            onClick={(e) => handleClickMore(e)}
            sx={{
              color: 'rgb(141, 148, 163)',
              '& svg': {
                maxWidth: '18px',
              },
            }}
          >
            <MoreHorizIcon fontSize="small" />
          </IconButton>
          <Menu
            anchorEl={moreAnchorEl}
            open={Boolean(moreAnchorEl)}
            onClose={() => setMoreAnchorEl(null)}
            PaperProps={{
              sx: {
                ...theme.components.MuiCssBaseline.styleOverrides['body']['& .border-color10'],
                borderWidth: 1,
                borderStyle: 'solid',
                borderRadius: '12px',
                minWidth: 160,
                '& .MuiMenu-list': {
                  backgroundColor: !isLightTheme && 'rgb(23, 23, 26)',
                  p: '2px!important',
                },
                '& .MuiMenuItem-root': {
                  borderRadius: '12px',
                  height: 40,
                  p: '12px',
                  '&:hover': {
                    backgroundColor: !isLightTheme && 'rgba(141, 148, 163, 0.04)',
                  },
                },
              },
            }}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            {channel.data.created_by?.['id'] && channel.data.created_by['id'] === channel._client.user.id && (
              <MenuItem onClick={onEditChannel}>
                <ListItemText
                  primary={
                    <Typography
                      className="font-family-graphik-medium"
                      variant="body2"
                      sx={{ color: theme.palette.secondary.main, pt: '4px' }}
                    >
                      Chat Settings
                    </Typography>
                  }
                />
              </MenuItem>
            )}
            {channel.data.created_by?.['id'] && channel.data.created_by['id'] === channel._client.user.id && (
              <Divider className="border-color10" />
            )}
            {channel.data.created_by['id'] !== channel._client.user.id && (
              <MenuItem onClick={handleViewMembers}>
                <ListItemText primary="View Members" />
              </MenuItem>
            )}
            <MenuItem disabled={requestDeleteChannel} onClick={onDeleteChannel}>
              <ListItemIcon sx={{ color: theme.palette.error.main, minWidth: '22px!important', mt: '-2px' }}>
                <SVGTrash width={14} height={14} />
              </ListItemIcon>
              <ListItemText
                primary={
                  <Typography className="font-family-graphik-medium" color="error" variant="body2">
                    {canEditChannel ? 'Delete Chat' : 'Leave Chat'}
                  </Typography>
                }
              />
            </MenuItem>
          </Menu>
        </Grid>
      </Grid>
      {openEditChannel && <AppMessagingEditChannel open={true} onClose={() => setOpenEditChannel(false)} />}
      {openDeleteWarning && (
        <AppWarning
          title={
            <Typography
              variant="h4"
              component="div"
              sx={{
                fontSize: 16,
                margin: 'auto',
                textAlign: 'center',
                padding: '2px 0px 0px 16px !important',
              }}
            >
              {canEditChannel
                ? 'Are you sure you want to delete the chat?'
                : 'Are you sure you want to leave the chat?'}
            </Typography>
          }
          content={
            <Typography
              variant="body1"
              component="div"
              sx={{
                color: isLightTheme ? '#696F7A' : '#ACACAC',
                mt: '-20px',
                mb: '0px',
                lineHeight: '20px',
              }}
            >
              {canEditChannel
                ? "Deleting a chat removes all message history, and means you won't receive any future messages sent within chat."
                : "Leaving a chat means you won't receive any future messages sent within this chat."}
            </Typography>
          }
          cancelButtonText="Cancel"
          proceedButtonText={canEditChannel ? 'Delete Chat' : 'Leave Chat'}
          showCloseButton={false}
          open={true}
          onClose={() => setOpenDeleteWarning(false)}
          onProceed={() => handleDeleteChannel()}
        />
      )}
      {openViewMembers && <MembersPopup />}
    </Box>
  )
}

export default React.memo(AppMessagingChannelHeader)
