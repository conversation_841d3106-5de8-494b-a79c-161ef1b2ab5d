import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import { useRouter } from 'next/router'
import React, { useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'

import { useStore } from '@/hooks/useStore'
import MemberItem from '@/memberup/components/member/member-item'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { selectMembersMap, setActiveMember } from '@/memberup/store/features/memberSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { roleCanManageCommunity } from '@/shared-libs/authorization'
import { IUser } from '@/shared-types/interfaces'

export default function MembersListing({ status }) {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const membership = useStore((state) => state.community.membership)
  const [activeTab, setActiveTab] = useState(status)
  const membersMap = useAppSelector((state) => selectMembersMap(state))
  const handleClickMember = async (item: IUser) => {
    dispatch(setActiveMember(item))
    dispatch(openDialog({ dialog: 'UserProfile', open: true, props: {} }))
  }

  const handleTabChange = (newTab) => {
    setActiveTab(newTab)
    router.replace(`${getCommunityBaseURL(membership)}/members/${newTab}`)
  }

  const allUsers = Object.values(membersMap)
  const admins = allUsers.filter((m: any) => roleCanManageCommunity(m?.role))
  const members = allUsers.filter((m: any) => !roleCanManageCommunity(m?.role))
  const onlineMembers = members.filter((m: any) => m.online)
  const onlineAdmins = admins.filter((o: any) => o.online)

  let numberOfUsers = activeTab === 'online' ? onlineMembers.length + onlineAdmins.length : allUsers.length
  let selectedMembers = activeTab === 'online' ? onlineAdmins.concat(onlineMembers) : admins.concat(members)

  return (
    <Box
      className="d-flex direction-column no-wrap"
      sx={{
        py: '16px',
        position: 'relative',
        height: '100%',
        width: '100%',
      }}
    >
      <Box sx={{ display: { xs: 'block', sm: 'none' }, marginBottom: 5 }}>
        <Grid
          sx={{
            marginLeft: { xs: '25px', sm: 'auto' },
            marginBottom: { xs: '-20px', sm: 'auto' },
            justifyContent: { xs: 'center', sm: 'flex-start' },
          }}
          container
          alignItems="center"
        ></Grid>
      </Box>
      <Box>
        <Grid
          container
          sx={{
            marginLeft: { xs: '25px', sm: 'auto' },
            justifyContent: { xs: 'center', sm: 'flex-start' },
          }}
        >
          <Grid item xs>
            <Tabs
              value={activeTab}
              indicatorColor="primary"
              textColor="primary"
              onChange={(event, newValue) => handleTabChange(newValue)}
              sx={{
                '& .MuiTab-root ': {
                  fontFamily: 'Graphik Semibold',
                  fontSize: '16px',
                },
              }}
            >
              <Tab value="all" label="All" />
              <Tab value="online" label="Online" />
            </Tabs>
          </Grid>
        </Grid>
      </Box>
      <Box
        id="members-wrapper"
        sx={{
          position: 'relative',
          display: 'flex',
          flexGrow: 1,
          justifyContent: 'center',
          alignItems: 'flex-start',
          width: { xs: '100%', sm: 'calc(100% + 24px)' },
          marginLeft: { xs: '10px', sm: '-12px' },
          marginRight: { xs: '10px', sm: '-12px' },
          overflowX: 'hidden',
          overflowY: 'auto',
        }}
      >
        <InfiniteScroll
          dataLength={numberOfUsers}
          next={() => {}}
          hasMore={false}
          loader={null}
          scrollableTarget="members-wrapper"
          style={{ overflow: 'hidden' }}
        >
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              flexGrow: 1,
              jsutifyContent: 'flex-start',
              alignItems: 'flex-start',
            }}
          >
            {selectedMembers.map((item: any, index) => (
              <Box
                key={item.id}
                sx={{
                  cursor: 'pointer',
                  display: (activeTab === 'online' && item.online) || activeTab === 'all' ? 'block' : 'none',
                  ml: { xs: 'auto', sm: 3 },
                  mr: { xs: 'auto', sm: 3 },
                  mt: { xs: 5, sm: 6 },
                }}
              >
                <MemberItem member={item} handleClick={() => handleClickMember(item)} />
              </Box>
            ))}
            {numberOfUsers === 0 && (
              <div className="inline-flex h-[214px] w-full flex-col items-center justify-center gap-2.5 rounded-base bg-black-500 p-4">
                <div className="font-['Graphik'] text-[14px] font-normal leading-loose text-black-100">No members.</div>
              </div>
            )}
          </Box>
        </InfiniteScroll>
      </Box>
    </Box>
  )
}
