{"name": "memberup-mono", "version": "0.0.0", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "pnpm": {"overrides": {"import-in-the-middle": "1.13.1", "require-in-the-middle": "7.5.2", "@types/react": "18.3.11", "@types/react-dom": "18.3.1", "axios@>=1.3.2 <=1.7.3": ">=1.7.4", "axios@>=1.0.0 <1.8.2": ">=1.8.2", "@babel/runtime@<7.26.10": ">=7.26.10", "next@>=15.3.0 <15.3.3": ">=15.3.3"}, "ignoredBuiltDependencies": ["@prisma/client", "@prisma/engines", "prisma"], "onlyBuiltDependencies": ["@sentry/cli", "@vercel/speed-insights", "core-js", "core-js-pure", "esbuild", "sharp", "unrs-resolver"]}, "scripts": {"ensure-babel-config": "[ -f apps/memberup/mod.js ] && mv -n apps/memberup/mod.js apps/memberup/.babelrc.js || true", "clean": "turbo run clean && rm -rf node_modules && rm -rf packages/shared/node_modules && rm -rf packages/database/node_modules && rm -rf apps/memberup-signup/node_modules && rm -rf apps/memberup/node_modules", "rebootstrap": "rm -rf node_modules && rm -rf packages/shared/node_modules && rm -rf packages/database/node_modules && rm -rf apps/memberup-signup/node_modules && rm -rf apps/memberup/node_modules && pnpm install", "build": "turbo run build", "build:fast": "turbo run build --cache-dir=.turbo", "check-deps": "pnpm outdated", "update-deps": "pnpm update", "check-types": "tsc --noEmit", "dev": "turbo run dev --parallel --continue", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md,mdx}\"", "start": "pnpm turbo run dev", "start:memberup": "turbo run dev --filter='memberup'", "typecheck": "turbo typecheck", "build:memberup": "turbo run build", "run:build:memberup": "turbo run start --filter='memberup'", "start:memberup-signup": "turbo run dev --filter='memberup-signup'", "prepare": "husky", "commit": "git-cz", "generate": "turbo run db:generate", "stream-chat-seed": "cli-confirm \"Are you sure you want to run seed? This will clear out the database.\" && turbo run stream-chat-seed --filter='memberup'", "pretest": "docker compose down -v && docker compose up -d && pnpm pretest:wait-for-db && turbo run db:generate && turbo run db:push:test", "pretest:wait-for-db": "turbo run wait-for-test-db", "test": "turbo run test --continue=always || true", "posttest": "docker compose down -v", "test:ci": "turbo run test --continue=always", "storybook": "turbo run storybook --filter='memberup'", "lint-staged": "lint-staged"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@commitlint/cz-commitlint": "19.8.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@memberup/eslint-config": "workspace:*", "@playwright/test": "^1.41.2", "cli-confirm": "^1.0.1", "commitizen": "4.3.1", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.14", "ts-node": "^10.9.2", "turbo": "^2.5.0", "typescript": "5.8.3"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "lint-staged": {"**/*.{ts,tsx,js,jsx,json,css,md,mdx}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "packageManager": "pnpm@10.13.1"}