name: CI

on:
  pull_request:
    branches:
      - development
      - staging
  push:
    branches:
      - development
      - staging
jobs:
  build:
    name: Build and Test
    timeout-minutes: 45
    runs-on: blacksmith-4vcpu-ubuntu-2404

    steps:
      - name: Check out code
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1

      - name: Setup Node.js environment
        uses: useblacksmith/setup-node@v5
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Install Doppler CLI
        uses: DopplerHQ/cli-action@v2

      - name: Build
        run: doppler run pnpm build
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN }}

      - name: Jest Test
        run: doppler run pnpm test-jest:ci
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN }}

      - name: Determine Slack Users to Mention
        id: determine_slack_users
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            authors=$(curl -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}/commits" | \
            jq -r '.[].commit.author.email' | sort | uniq | jq -Rs .)
            echo "SLACK_USERS=$(node ./apps/memberup/scripts/getSlackId.js ${{ github.actor }})" >> $GITHUB_ENV
          else
            echo "SLACK_USERS=$(node ./apps/memberup/scripts/getSlackId.js $authors)" >> $GITHUB_ENV
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Notify Slack on Failure or Cancellation
        if: failure() || cancelled()
        uses: ravsamhq/notify-slack-action@v2
        with:
          status: ${{ job.status }}
          notification_title: 'Build and Test Job - ${{ job.status }} on ${{ github.ref }} - <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Run>'
          message_format: ':fire: *Build and Test Job* ${{ job.status }} in <${{ github.server_url }}/${{ github.repository }}/${{ github.ref }}|${{ github.repository }}>'
          mention_users: ${{ env.SLACK_USERS }}
          footer: 'Linked Repo <${{ github.server_url }}/${{ github.repository }}|${{ github.repository }}> | <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Run>'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
