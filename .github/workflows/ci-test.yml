name: CI Tests

on:
  pull_request:
    branches:
      - ua-merge
      - development
      - main
  push:
    branches:
      - ua-merge
      - development
      - main

jobs:
  test:
    name: Run Tests
    runs-on: blacksmith-4vcpu-ubuntu-2404
    timeout-minutes: 30

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: useblacksmith/setup-node@v5
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Cache pnpm store
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: pnpm-store-${{ runner.os }}-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            pnpm-store-${{ runner.os }}-

      - name: Install dependencies
        run: pnpm install

      - name: Setup test database
        run: |
          docker compose down
          docker compose up -d

      - name: Wait for database to be ready
        run: pnpm pretest:wait-for-db

      - name: Setup prisma client and push schema
        run: |
          pnpm generate
          pnpm turbo run db:push:test

      - name: Run tests
        run: pnpm test:ci
