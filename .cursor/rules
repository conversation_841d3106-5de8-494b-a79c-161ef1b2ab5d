# MemberUp Monorepo Cursor Rules

## Project Overview

- Monorepo structure using Turborepo
- Built with TypeScript 5.6.3, React 18.3.1, Next.js 15.3.0, Node.js 22.x and pnpm 10.13.1
- Main app: `apps/memberup` (App Router + Pages Router for the API)
- Packages: `database`, `eslint-config`, `shared`, `tsconfig`, `ui`

## Code Style and Formatting

### Prettier Configuration

- Line width: 120 characters
- Single quotes for strings
- NO semicolons at the end of statements
- Trailing commas: always
- Arrow parentheses: always
- Tab width: 2 spaces
- JSX single quotes: false (use double quotes)

### Import Organization

Import order (enforced by prettier):

1. Third-party modules
2. Empty line
3. Current directory imports (`./`)
4. `@memberup/` imports
5. `@/` aliased imports
6. Other relative imports (excluding CSS)
7. CSS imports

### TypeScript Configuration

- Target: ES5
- Module: ESNext
- JSX: Preserve
- Strict mode: DISABLED
- Strict null checks: DISABLED
- No implicit any: DISABLED
- Incremental compilation: enabled
- Module resolution: Node

## File and Folder Structure

### Component Structure

- Use barrel exports pattern: `index.tsx` that exports from implementation files
- Component folders use kebab-case: `login-form/`, `user-details/`
- Implementation files also use kebab-case: `login-form.tsx`
- Example structure:

  ```plaintext
  components/
    auth/
      login-form/
        index.tsx        # export * from './login-form'
        login-form.tsx   # actual component implementation
  ```

### Path Aliases

Use these path aliases instead of relative imports:

- `@/` - root of the app
- `@/components/*` - components directory
- `@/lib/*` - lib directory
- `@/memberup/components/*` - src/components
- `@/memberup/libs/*` - src/libs
- `@/shared-components/*` - packages/shared/src/components
- `@/shared-libs/*` - packages/shared/src/libs
- `@/shared-types/*` - packages/shared/src/types

## Import Rules

- ❌ NO relative imports from parent directories (`../`)
- ✅ Same directory imports are allowed (`./`)
- ✅ Use path aliases for cross-directory imports
- ✅ Prefer absolute imports using aliases

## React and Component Guidelines

### Component Definition

- Use standard functions for components whenever possible
- Export components using named exports
- Use TypeScript interfaces for props with 3+ properties or when the props will be reused, and inline types for simpler cases

```tsx
// ✅ Simple components (≤3 props) - inline types
function Button({ text, onClick }: { text: string; onClick: () => void }) {
  return <button onClick={onClick}>{text}</button>
}

// ✅ Complex components (>3 props) - interfaces
interface FormProps {
  onSubmit: (data: FormData) => void
  validation: ValidationRules
  loading: boolean
  error?: string
}
```

### Hooks and State

- Custom hooks in `hooks/` directory
- State management: Redux Toolkit (will be phased out), Zustand
- Form handling: react-hook-form with zod validation

## Styling Guidelines

### TailwindCSS

- Primary styling method
- Custom theme with extensive color palette
- Use Tailwind classes directly, avoid custom CSS
- Important flag is enabled globally for compatibility between Tailwind & Material UI

### Shadcn/UI Components

- Located in `components/ui/`
- Style: "new-york"
- CSS variables enabled
- Base color: neutral

### Color Naming Convention

- Primary colors: `primary-100`, `primary-200`, etc.
- Grays: `grey-100` to `grey-900`
- Community colors: `community-primary`, `community-secondary`

## Database and API

### Prisma

- Schema location: `packages/database/prisma/schema.prisma`
- Generate command: `turbo run db:generate`
- Always run `turbo run db:generate` after schema changes

### API Routes

- Location: `pages/api/` (Pages Router API routes)
- Next.js App Router: `app/api/` (Only one NextAuth route is hosted in the app router)
- Use TypeScript for all API routes

## Testing

- Unit tests: Jest
- E2E tests: Playwright (not currently in use)
- Test files: `*.spec.ts`, `*.test.ts`
- Run tests: `pnpm test`

## Git and Version Control

### Commit Convention

- Use Git commit
- Follow conventional commits format
- Commitlint enforces standards

### Branch Naming

- Feature: `feature/description-TICKET`
- Chore: `chore/description-TICKET`
- Fix: `fix/description-TICKET`

## Development Commands

- `pnpm dev` - Start all apps in development
- `pnpm start:memberup` - Start only memberup app
- `pnpm build` - Build all apps
- `pnpm lint` - Run linting
- `pnpm format` - Format code with Prettier
- `pnpm typecheck` - Run TypeScript type checking

## Environment Variables

- Managed by Doppler
- ESLint warns on undeclared env vars

## Best Practices

1. NO console.log in production code. Another function, warn, from @/libs/error-handling can be used instead. It will, in the end, execute console.warn, but only while in development mode. We still only want to use it when it's really necessary in order to allow for some level of traceability and prevent issues when debugging a malfunction whose origin ends up being hard to identify.
2. Handle errors properly with try-catch
3. Use TypeScript types extensively
4. Keep components small and focused
5. Extract reusable logic to custom hooks
6. Use path aliases instead of relative imports
7. Follow the established file/folder naming conventions
8. Write meaningful commit messages
9. Keep barrel exports clean and organized
10. Prefer composition over inheritance

## Technologies and Libraries

### Core Framework

- **Next.js 15**
- **TypeScript 5.6.3**
- **React 18.3.1**
- **Node.js 22.x**

### Styling and UI Components

- **Tailwind CSS 3.4.1** - Primary styling framework with custom theme
- **Shadcn/ui** - Component library built on Radix UI
- **Radix UI** - Accessible, unstyled component primitives
- **Material UI 5.x** - Legacy component library (being phased out)
- **Framer Motion** - Animation library

### Form Handling and Validation

- **React Hook Form** - Primary form library
- **Zod** - Schema validation for forms and API routes

### State Management

- **Zustand** - Modern state management (preferred)
- **Redux Toolkit** with Redux Saga - Legacy state management (being phased out)
- **SWR** - Server state caching and synchronization
- **React Context** - Used for global Providers

### Database and Backend

- **Prisma ORM** - Database toolkit and query builder
- **PlanetScale** - MySQL database hosting
- **NextAuth.js 5.0** - Authentication framework
- **JWT** - Session management and invite tokens
- **bcrypt** - Password hashing

### External Services

#### Payment Processing

- **Stripe** - Payment operations and subscription management
- **Stripe Connect** - Creator payouts and affiliate payments

#### Media and Content

- **Cloudinary** - Image/video upload, storage, and optimization
- **Mux** - Video upload, encoding and streaming
- **PDF.js** - Document viewing
- **Giphy** - GIF integration
- **Unsplash** - Stock images

#### Real-time and Communication

- **Stream Chat** - Messaging and community feed (being phased out)

#### Notifications and Marketing

- **Knock** - Multi-channel notifications (email, in-app, push)
- **Postmark** - Transactional emails
- **Active Campaign** - Email marketing automation

#### Search and Discovery

- **Algolia** - Full-text search across feed, content library, and members
- **MiniSearch** - Client-side search functionality

#### Infrastructure and Monitoring

- **Vercel** - Hosting and deployments
- **Sentry** - Error tracking and performance monitoring
- **PostHog** - Product analytics and feature flags
- **Inngest** - Background job processing and webhook handling

#### Text Editing

- **TipTap** - Rich text editor (preferred)
- **Draft.js** - Legacy rich text editor (being phased out)

### Development Tools

- **Turborepo** - Monorepo build system
- **pnpm Workspaces** - Package management
- **Storybook** - Component development and documentation
- **Jest** - Unit testing framework
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Husky** - Git hooks
- **Doppler** - Environment variable management

### Important Notes

- Transitioning from Material UI to Tailwind CSS + Shadcn/ui
- Phasing out Redux Toolkit in favor of Zustand
- Stream Chat being replaced with custom solution
- Draft.js being replaced with TipTap
