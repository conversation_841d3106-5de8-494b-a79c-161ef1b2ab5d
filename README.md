# MemberUp Monorepo

This is the MemberUp Main monorepo which consists of `apps/memberup` (main app), and `packages/shared` (shared logic and components).

## How to install/run

- We use pnpm and pnpm workspaces. Run `pnpm rebootstrap` to install all dependencies.
- We also use doppler for secret management. Install [Doppler](https://doppler.com/) CLI and run `doppler setup` to login and configure your local environment. By default, the project will use the `dev` environment.
- Finally, install Turborepo CLI (`npm install -g turbo`). We use turborepo to manage the project monorepo
- Run `doppler run turbo db:generate` to generate Prisma client
- Run `doppler run turbo dev` to start the development server. The outputs will be available at `http://localhost:3000` (main app) and `http://start.localhost:3001` (signup app).

## How to update Prisma Schema

- Make your changes in `packages/database/prisma/schema.prisma`
- Re-generate the Prisma client: `doppler run turbo db:generate`
- Push the changes to the database server: `doppler run turbo db:push`

## How to commit/deploy

- We use [Vercel](https://vercel.com/) configed to deploy from a push to git.
- Create a new branch off of `development` and make your changes.
- If you prefere so, run `pnpm commit` to commit your changes. This will run `commitizen` to create a commit message.
- We use husky for pre-commit hooks to run linting, build and tests.
- Push your branch to GitHub and create a pull request to merge into `development`.

## Running Tests

### Integration Tests

⚠️ Integration tests requires docker to be running.

```bash
pnpm test
```

### e2e Tests

```bash
PLAYWRIGHT_APP_DOMAIN="localhost:3000" \
PLAYWRIGHT_APP_PROTOCOL="HTTP" \
doppler run --preserve-env -- npx playwright test --ui
```
